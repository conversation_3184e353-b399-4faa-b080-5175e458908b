package com.example.castapp.model;

/**
 * 🪟 投屏窗口容器可视化数据类
 * 用于在远程接收端控制窗口中绘制投屏窗口容器的可视化框架
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\u0006\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b`\b\u0086\b\u0018\u0000 \u0080\u00012\u00020\u0001:\u0002\u0080\u0001B\u00db\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000b\u001a\u00020\u0006\u0012\u0006\u0010\f\u001a\u00020\t\u0012\u0006\u0010\r\u001a\u00020\u0006\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u0012\u0006\u0010\u0010\u001a\u00020\u0006\u0012\u0006\u0010\u0011\u001a\u00020\u0006\u0012\u0006\u0010\u0012\u001a\u00020\u0006\u0012\u0006\u0010\u0013\u001a\u00020\u0006\u0012\u0006\u0010\u0014\u001a\u00020\u0015\u0012\u0006\u0010\u0016\u001a\u00020\u0015\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0018\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0019\u001a\u00020\u000f\u0012\b\b\u0002\u0010\u001a\u001a\u00020\u000f\u0012\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u001c\u0012\b\b\u0002\u0010\u001d\u001a\u00020\u000f\u0012\b\b\u0002\u0010\u001e\u001a\u00020\t\u0012\b\b\u0002\u0010\u001f\u001a\u00020\u0006\u0012\n\b\u0002\u0010 \u001a\u0004\u0018\u00010!\u0012\b\b\u0002\u0010\"\u001a\u00020\u0003\u0012\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010$\u001a\u00020\u000f\u0012\b\b\u0002\u0010%\u001a\u00020\u000f\u0012\b\b\u0002\u0010&\u001a\u00020\t\u0012\n\b\u0002\u0010\'\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010(\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010)\u001a\u00020\u0006\u0012\b\b\u0002\u0010*\u001a\u00020\t\u0012\b\b\u0002\u0010+\u001a\u00020\u000f\u0012\b\b\u0002\u0010,\u001a\u00020\t\u00a2\u0006\u0002\u0010-J\t\u0010R\u001a\u00020\u0003H\u00c6\u0003J\t\u0010S\u001a\u00020\u000fH\u00c6\u0003J\t\u0010T\u001a\u00020\u0006H\u00c6\u0003J\t\u0010U\u001a\u00020\u0006H\u00c6\u0003J\t\u0010V\u001a\u00020\u0006H\u00c6\u0003J\t\u0010W\u001a\u00020\u0006H\u00c6\u0003J\t\u0010X\u001a\u00020\u0015H\u00c6\u0003J\t\u0010Y\u001a\u00020\u0015H\u00c6\u0003J\t\u0010Z\u001a\u00020\u0006H\u00c6\u0003J\t\u0010[\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\\\u001a\u00020\u000fH\u00c6\u0003J\u000b\u0010]\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010^\u001a\u00020\u000fH\u00c6\u0003J\u000b\u0010_\u001a\u0004\u0018\u00010\u001cH\u00c6\u0003J\t\u0010`\u001a\u00020\u000fH\u00c6\u0003J\t\u0010a\u001a\u00020\tH\u00c6\u0003J\t\u0010b\u001a\u00020\u0006H\u00c6\u0003J\u000b\u0010c\u001a\u0004\u0018\u00010!H\u00c6\u0003J\t\u0010d\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010f\u001a\u00020\u000fH\u00c6\u0003J\t\u0010g\u001a\u00020\u000fH\u00c6\u0003J\t\u0010h\u001a\u00020\u0006H\u00c6\u0003J\t\u0010i\u001a\u00020\tH\u00c6\u0003J\u000b\u0010j\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010k\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010l\u001a\u00020\u0006H\u00c6\u0003J\t\u0010m\u001a\u00020\tH\u00c6\u0003J\t\u0010n\u001a\u00020\u000fH\u00c6\u0003J\t\u0010o\u001a\u00020\tH\u00c6\u0003J\t\u0010p\u001a\u00020\u0006H\u00c6\u0003J\t\u0010q\u001a\u00020\tH\u00c6\u0003J\t\u0010r\u001a\u00020\tH\u00c6\u0003J\t\u0010s\u001a\u00020\u0006H\u00c6\u0003J\t\u0010t\u001a\u00020\tH\u00c6\u0003J\t\u0010u\u001a\u00020\u0006H\u00c6\u0003J\u00fd\u0002\u0010v\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\u00062\b\b\u0002\u0010\f\u001a\u00020\t2\b\b\u0002\u0010\r\u001a\u00020\u00062\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u00062\b\b\u0002\u0010\u0011\u001a\u00020\u00062\b\b\u0002\u0010\u0012\u001a\u00020\u00062\b\b\u0002\u0010\u0013\u001a\u00020\u00062\b\b\u0002\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u00152\b\b\u0002\u0010\u0017\u001a\u00020\u00062\b\b\u0002\u0010\u0018\u001a\u00020\u00062\b\b\u0002\u0010\u0019\u001a\u00020\u000f2\b\b\u0002\u0010\u001a\u001a\u00020\u000f2\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\b\b\u0002\u0010\u001d\u001a\u00020\u000f2\b\b\u0002\u0010\u001e\u001a\u00020\t2\b\b\u0002\u0010\u001f\u001a\u00020\u00062\n\b\u0002\u0010 \u001a\u0004\u0018\u00010!2\b\b\u0002\u0010\"\u001a\u00020\u00032\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010$\u001a\u00020\u000f2\b\b\u0002\u0010%\u001a\u00020\u000f2\b\b\u0002\u0010&\u001a\u00020\t2\n\b\u0002\u0010\'\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010(\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010)\u001a\u00020\u00062\b\b\u0002\u0010*\u001a\u00020\t2\b\b\u0002\u0010+\u001a\u00020\u000f2\b\b\u0002\u0010,\u001a\u00020\tH\u00c6\u0001J\u0013\u0010w\u001a\u00020\u000f2\b\u0010x\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u0010y\u001a\u00020\u0003J\u0006\u0010z\u001a\u00020\u0003J\u0006\u0010{\u001a\u00020\u0003J\u0006\u0010|\u001a\u00020\u001cJ\t\u0010}\u001a\u00020\tH\u00d6\u0001J\u0006\u0010~\u001a\u00020\u000fJ\t\u0010\u007f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0017\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010/R\u0011\u0010\u001e\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u00101R\u0011\u0010\u001f\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010/R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u00104R\u0011\u0010\u0016\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u00106R\u0011\u0010\u0018\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u0010/R\u0013\u0010\u001b\u001a\u0004\u0018\u00010\u001c\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u00109R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b:\u00104R\u0013\u0010(\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u00104R\u0013\u0010\'\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u00104R\u0011\u0010&\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u00101R\u0011\u0010$\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010>R\u0011\u0010\u001d\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010>R\u0011\u0010\u001a\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010>R\u0011\u0010%\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010>R\u0011\u0010\u0019\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010>R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010>R\u0011\u0010+\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010>R\u0011\u0010)\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010/R\u0011\u0010\n\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u00101R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u00101R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u0010/R\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u0010/R\u0011\u0010\u0014\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\bD\u00106R\u0013\u0010#\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bE\u00104R\u0011\u0010\u000b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bF\u0010/R\u0011\u0010\r\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bG\u0010/R\u0013\u0010 \u001a\u0004\u0018\u00010!\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u0010IR\u0011\u0010*\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\bJ\u00101R\u0011\u0010\"\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bK\u00104R\u0011\u0010\u0013\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bL\u0010/R\u0011\u0010\u0012\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bM\u0010/R\u0011\u0010\u0010\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bN\u0010/R\u0011\u0010\u0011\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bO\u0010/R\u0011\u0010,\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\bP\u00101R\u0011\u0010\f\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\bQ\u00101\u00a8\u0006\u0081\u0001"}, d2 = {"Lcom/example/castapp/model/WindowVisualizationData;", "", "connectionId", "", "deviceName", "originalX", "", "originalY", "originalWidth", "", "originalHeight", "rotationAngle", "zOrder", "scaleFactor", "isVisible", "", "visualizedX", "visualizedY", "visualizedWidth", "visualizedHeight", "remoteControlScale", "", "containerScale", "alpha", "cornerRadius", "isMirrored", "isCropping", "cropRectRatio", "Landroid/graphics/RectF;", "isBorderEnabled", "borderColor", "borderWidth", "screenshotBitmap", "Landroid/graphics/Bitmap;", "textContent", "richTextData", "isBold", "isItalic", "fontSize", "fontName", "fontFamily", "lineSpacing", "textAlignment", "isWindowColorEnabled", "windowBackgroundColor", "(Ljava/lang/String;Ljava/lang/String;FFIIFIFZFFFFDDFFZZLandroid/graphics/RectF;ZIFLandroid/graphics/Bitmap;Ljava/lang/String;Ljava/lang/String;ZZILjava/lang/String;Ljava/lang/String;FIZI)V", "getAlpha", "()F", "getBorderColor", "()I", "getBorderWidth", "getConnectionId", "()Ljava/lang/String;", "getContainerScale", "()D", "getCornerRadius", "getCropRectRatio", "()Landroid/graphics/RectF;", "getDeviceName", "getFontFamily", "getFontName", "getFontSize", "()Z", "getLineSpacing", "getOriginalHeight", "getOriginalWidth", "getOriginalX", "getOriginalY", "getRemoteControlScale", "getRichTextData", "getRotationAngle", "getScaleFactor", "getScreenshotBitmap", "()Landroid/graphics/Bitmap;", "getTextAlignment", "getTextContent", "getVisualizedHeight", "getVisualizedWidth", "getVisualizedX", "getVisualizedY", "getWindowBackgroundColor", "getZOrder", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component29", "component3", "component30", "component31", "component32", "component33", "component34", "component35", "component36", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "getDeviceDisplayInfo", "getShortConnectionId", "getSizeText", "getVisualizationBounds", "hashCode", "shouldDisplay", "toString", "Companion", "app_debug"})
public final class WindowVisualizationData {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String connectionId = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String deviceName = null;
    private final float originalX = 0.0F;
    private final float originalY = 0.0F;
    private final int originalWidth = 0;
    private final int originalHeight = 0;
    private final float rotationAngle = 0.0F;
    private final int zOrder = 0;
    private final float scaleFactor = 0.0F;
    private final boolean isVisible = false;
    private final float visualizedX = 0.0F;
    private final float visualizedY = 0.0F;
    private final float visualizedWidth = 0.0F;
    private final float visualizedHeight = 0.0F;
    private final double remoteControlScale = 0.0;
    private final double containerScale = 0.0;
    private final float alpha = 0.0F;
    private final float cornerRadius = 0.0F;
    private final boolean isMirrored = false;
    private final boolean isCropping = false;
    @org.jetbrains.annotations.Nullable()
    private final android.graphics.RectF cropRectRatio = null;
    private final boolean isBorderEnabled = false;
    private final int borderColor = 0;
    private final float borderWidth = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private final android.graphics.Bitmap screenshotBitmap = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String textContent = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String richTextData = null;
    private final boolean isBold = false;
    private final boolean isItalic = false;
    private final int fontSize = 0;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String fontName = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String fontFamily = null;
    private final float lineSpacing = 0.0F;
    private final int textAlignment = 0;
    private final boolean isWindowColorEnabled = false;
    private final int windowBackgroundColor = 0;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.model.WindowVisualizationData.Companion Companion = null;
    
    public WindowVisualizationData(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.Nullable()
    java.lang.String deviceName, float originalX, float originalY, int originalWidth, int originalHeight, float rotationAngle, int zOrder, float scaleFactor, boolean isVisible, float visualizedX, float visualizedY, float visualizedWidth, float visualizedHeight, double remoteControlScale, double containerScale, float alpha, float cornerRadius, boolean isMirrored, boolean isCropping, @org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRectRatio, boolean isBorderEnabled, int borderColor, float borderWidth, @org.jetbrains.annotations.Nullable()
    android.graphics.Bitmap screenshotBitmap, @org.jetbrains.annotations.NotNull()
    java.lang.String textContent, @org.jetbrains.annotations.Nullable()
    java.lang.String richTextData, boolean isBold, boolean isItalic, int fontSize, @org.jetbrains.annotations.Nullable()
    java.lang.String fontName, @org.jetbrains.annotations.Nullable()
    java.lang.String fontFamily, float lineSpacing, int textAlignment, boolean isWindowColorEnabled, int windowBackgroundColor) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getConnectionId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getDeviceName() {
        return null;
    }
    
    public final float getOriginalX() {
        return 0.0F;
    }
    
    public final float getOriginalY() {
        return 0.0F;
    }
    
    public final int getOriginalWidth() {
        return 0;
    }
    
    public final int getOriginalHeight() {
        return 0;
    }
    
    public final float getRotationAngle() {
        return 0.0F;
    }
    
    public final int getZOrder() {
        return 0;
    }
    
    public final float getScaleFactor() {
        return 0.0F;
    }
    
    public final boolean isVisible() {
        return false;
    }
    
    public final float getVisualizedX() {
        return 0.0F;
    }
    
    public final float getVisualizedY() {
        return 0.0F;
    }
    
    public final float getVisualizedWidth() {
        return 0.0F;
    }
    
    public final float getVisualizedHeight() {
        return 0.0F;
    }
    
    public final double getRemoteControlScale() {
        return 0.0;
    }
    
    public final double getContainerScale() {
        return 0.0;
    }
    
    public final float getAlpha() {
        return 0.0F;
    }
    
    public final float getCornerRadius() {
        return 0.0F;
    }
    
    public final boolean isMirrored() {
        return false;
    }
    
    public final boolean isCropping() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.RectF getCropRectRatio() {
        return null;
    }
    
    public final boolean isBorderEnabled() {
        return false;
    }
    
    public final int getBorderColor() {
        return 0;
    }
    
    public final float getBorderWidth() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.Bitmap getScreenshotBitmap() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTextContent() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRichTextData() {
        return null;
    }
    
    public final boolean isBold() {
        return false;
    }
    
    public final boolean isItalic() {
        return false;
    }
    
    public final int getFontSize() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFontName() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFontFamily() {
        return null;
    }
    
    public final float getLineSpacing() {
        return 0.0F;
    }
    
    public final int getTextAlignment() {
        return 0;
    }
    
    public final boolean isWindowColorEnabled() {
        return false;
    }
    
    public final int getWindowBackgroundColor() {
        return 0;
    }
    
    /**
     * 获取简化的连接ID（显示规则）
     * - 摄像头窗口：显示完整ID
     * - 媒体窗口：显示文件名前8位
     * - 文本窗口：显示UUID后8位
     * - 投屏窗口：显示后8位
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getShortConnectionId() {
        return null;
    }
    
    /**
     * 获取设备显示信息
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDeviceDisplayInfo() {
        return null;
    }
    
    /**
     * 获取可视化窗口的边界矩形
     */
    @org.jetbrains.annotations.NotNull()
    public final android.graphics.RectF getVisualizationBounds() {
        return null;
    }
    
    /**
     * 检查是否需要显示（可见且有有效尺寸）
     */
    public final boolean shouldDisplay() {
        return false;
    }
    
    /**
     * 获取尺寸显示文本
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSizeText() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final float component11() {
        return 0.0F;
    }
    
    public final float component12() {
        return 0.0F;
    }
    
    public final float component13() {
        return 0.0F;
    }
    
    public final float component14() {
        return 0.0F;
    }
    
    public final double component15() {
        return 0.0;
    }
    
    public final double component16() {
        return 0.0;
    }
    
    public final float component17() {
        return 0.0F;
    }
    
    public final float component18() {
        return 0.0F;
    }
    
    public final boolean component19() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    public final boolean component20() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.RectF component21() {
        return null;
    }
    
    public final boolean component22() {
        return false;
    }
    
    public final int component23() {
        return 0;
    }
    
    public final float component24() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.Bitmap component25() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component26() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component27() {
        return null;
    }
    
    public final boolean component28() {
        return false;
    }
    
    public final boolean component29() {
        return false;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    public final int component30() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component31() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component32() {
        return null;
    }
    
    public final float component33() {
        return 0.0F;
    }
    
    public final int component34() {
        return 0;
    }
    
    public final boolean component35() {
        return false;
    }
    
    public final int component36() {
        return 0;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    public final int component5() {
        return 0;
    }
    
    public final int component6() {
        return 0;
    }
    
    public final float component7() {
        return 0.0F;
    }
    
    public final int component8() {
        return 0;
    }
    
    public final float component9() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.WindowVisualizationData copy(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.Nullable()
    java.lang.String deviceName, float originalX, float originalY, int originalWidth, int originalHeight, float rotationAngle, int zOrder, float scaleFactor, boolean isVisible, float visualizedX, float visualizedY, float visualizedWidth, float visualizedHeight, double remoteControlScale, double containerScale, float alpha, float cornerRadius, boolean isMirrored, boolean isCropping, @org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRectRatio, boolean isBorderEnabled, int borderColor, float borderWidth, @org.jetbrains.annotations.Nullable()
    android.graphics.Bitmap screenshotBitmap, @org.jetbrains.annotations.NotNull()
    java.lang.String textContent, @org.jetbrains.annotations.Nullable()
    java.lang.String richTextData, boolean isBold, boolean isItalic, int fontSize, @org.jetbrains.annotations.Nullable()
    java.lang.String fontName, @org.jetbrains.annotations.Nullable()
    java.lang.String fontFamily, float lineSpacing, int textAlignment, boolean isWindowColorEnabled, int windowBackgroundColor) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\"\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u00a8\u0006\u000b"}, d2 = {"Lcom/example/castapp/model/WindowVisualizationData$Companion;", "", "()V", "fromCastWindowInfo", "Lcom/example/castapp/model/WindowVisualizationData;", "windowInfo", "Lcom/example/castapp/model/CastWindowInfo;", "remoteControlScale", "", "receiverId", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 从CastWindowInfo创建WindowVisualizationData
         * @param windowInfo 投屏窗口信息
         * @param remoteControlScale 远程控制窗口的缩放比例
         * @return 可视化数据对象
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.model.WindowVisualizationData fromCastWindowInfo(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo windowInfo, double remoteControlScale, @org.jetbrains.annotations.Nullable()
        java.lang.String receiverId) {
            return null;
        }
    }
}