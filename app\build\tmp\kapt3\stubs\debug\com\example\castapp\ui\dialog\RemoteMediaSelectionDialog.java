package com.example.castapp.ui.dialog;

/**
 * 简单的媒体选择对话框
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000r\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001:\u0001(B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0010\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0014\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00110\u0010H\u0002J\b\u0010\u0012\u001a\u00020\u0013H\u0002J\u0010\u0010\u0014\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\u0015\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\u0010\u0010\u0016\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0002J\b\u0010\u0017\u001a\u00020\u0018H\u0002J.\u0010\u0019\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001a2\u0014\b\u0002\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u001a\u0012\u0004\u0012\u00020\u00010\u001dH\u0002J\u0018\u0010\u001e\u001a\u00020\f2\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"H\u0002J\u0006\u0010#\u001a\u00020\fJ\u0016\u0010$\u001a\u00020\f2\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\'0&H\u0002R\u000e\u0010\u0007\u001a\u00020\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006)"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteMediaSelectionDialog;", "", "context", "Landroid/content/Context;", "remoteReceiverConnection", "Lcom/example/castapp/model/RemoteReceiverConnection;", "(Landroid/content/Context;Lcom/example/castapp/model/RemoteReceiverConnection;)V", "connectionManager", "Lcom/example/castapp/manager/RemoteConnectionManager;", "remoteReceiverManager", "Lcom/example/castapp/manager/RemoteReceiverManager;", "createCameraPlaceholder", "", "mediaType", "Lcom/example/castapp/ui/dialog/RemoteMediaSelectionDialog$MediaType;", "getReceiverScreenResolution", "Lkotlin/Pair;", "", "getRemoteControlScale", "", "handleLocalContainerMode", "handleMediaSelection", "handleRealTimeSyncMode", "isRealTimeSyncEnabled", "", "sendAddMediaMessage", "", "displayName", "extraData", "", "setupClickListeners", "dialogView", "Landroid/view/View;", "dialog", "Landroid/app/AlertDialog;", "show", "triggerVisualizationUpdate", "windowInfoList", "", "Lcom/example/castapp/model/CastWindowInfo;", "MediaType", "app_debug"})
public final class RemoteMediaSelectionDialog {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.RemoteReceiverManager remoteReceiverManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.RemoteConnectionManager connectionManager = null;
    
    public RemoteMediaSelectionDialog(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection remoteReceiverConnection) {
        super();
    }
    
    /**
     * 显示媒体选择对话框
     */
    public final void show() {
    }
    
    /**
     * 设置点击监听器
     */
    private final void setupClickListeners(android.view.View dialogView, android.app.AlertDialog dialog) {
    }
    
    /**
     * 处理媒体选择
     */
    private final void handleMediaSelection(com.example.castapp.ui.dialog.RemoteMediaSelectionDialog.MediaType mediaType) {
    }
    
    /**
     * 处理实时同步模式
     */
    private final void handleRealTimeSyncMode(com.example.castapp.ui.dialog.RemoteMediaSelectionDialog.MediaType mediaType) {
    }
    
    /**
     * 处理本地容器模式
     */
    private final void handleLocalContainerMode(com.example.castapp.ui.dialog.RemoteMediaSelectionDialog.MediaType mediaType) {
    }
    
    /**
     * 创建摄像头占位容器
     */
    private final void createCameraPlaceholder(com.example.castapp.ui.dialog.RemoteMediaSelectionDialog.MediaType mediaType) {
    }
    
    /**
     * 发送添加媒体消息
     */
    private final void sendAddMediaMessage(java.lang.String mediaType, java.lang.String displayName, java.util.Map<java.lang.String, ? extends java.lang.Object> extraData) {
    }
    
    /**
     * 检查实时同步状态
     */
    private final boolean isRealTimeSyncEnabled() {
        return false;
    }
    
    /**
     * 获取接收端屏幕分辨率
     */
    private final kotlin.Pair<java.lang.Integer, java.lang.Integer> getReceiverScreenResolution() {
        return null;
    }
    
    /**
     * 获取遥控端窗口缩放倍数
     * 🎯 简化：使用默认缩放倍数，避免复杂计算
     */
    private final double getRemoteControlScale() {
        return 0.0;
    }
    
    /**
     * 触发可视化更新
     * 🎯 关键修复：通知RemoteReceiverControlDialog更新窗口显示
     */
    private final void triggerVisualizationUpdate(java.util.List<com.example.castapp.model.CastWindowInfo> windowInfoList) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0005R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007j\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\r\u00a8\u0006\u000e"}, d2 = {"Lcom/example/castapp/ui/dialog/RemoteMediaSelectionDialog$MediaType;", "", "displayName", "", "messageType", "(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V", "getDisplayName", "()Ljava/lang/String;", "getMessageType", "FRONT_CAMERA", "REAR_CAMERA", "VIDEO", "PICTURE", "TEXT", "app_debug"})
    public static enum MediaType {
        /*public static final*/ FRONT_CAMERA /* = new FRONT_CAMERA(null, null) */,
        /*public static final*/ REAR_CAMERA /* = new REAR_CAMERA(null, null) */,
        /*public static final*/ VIDEO /* = new VIDEO(null, null) */,
        /*public static final*/ PICTURE /* = new PICTURE(null, null) */,
        /*public static final*/ TEXT /* = new TEXT(null, null) */;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String displayName = null;
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String messageType = null;
        
        MediaType(java.lang.String displayName, java.lang.String messageType) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getDisplayName() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getMessageType() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.example.castapp.ui.dialog.RemoteMediaSelectionDialog.MediaType> getEntries() {
            return null;
        }
    }
}