package com.example.castapp.model;

/**
 * 🎯 统一的遥控端窗口配置管理器
 * 集中管理所有接收端的窗口参数，避免数据分散和不一致
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000l\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010$\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u0000 ,2\u00020\u0001:\u0001,B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J \u0010\t\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u000b0\n2\u0006\u0010\f\u001a\u00020\u0007J\u0018\u0010\r\u001a\u0004\u0018\u00010\b2\u0006\u0010\f\u001a\u00020\u00072\u0006\u0010\u000e\u001a\u00020\u0007J\u0016\u0010\u000f\u001a\u00020\u00102\u0006\u0010\f\u001a\u00020\u00072\u0006\u0010\u000e\u001a\u00020\u0007J\u0016\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\f\u001a\u00020\u0007J\u001c\u0010\u0014\u001a\u00020\u00102\u0006\u0010\f\u001a\u00020\u00072\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00160\nJ(\u0010\u0017\u001a\u00020\u00102\u0006\u0010\f\u001a\u00020\u00072\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00190\n2\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0013J*\u0010\u001a\u001a\u00020\u00102\u0006\u0010\f\u001a\u00020\u00072\u0006\u0010\u000e\u001a\u00020\u00072\u0006\u0010\u001b\u001a\u00020\u00072\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u0007Jo\u0010\u001d\u001a\u00020\u00102\u0006\u0010\f\u001a\u00020\u00072\u0006\u0010\u000e\u001a\u00020\u00072\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u001f2\n\b\u0002\u0010 \u001a\u0004\u0018\u00010\u001f2\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\"2\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010$\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010%\u001a\u0004\u0018\u00010&2\n\b\u0002\u0010\'\u001a\u0004\u0018\u00010\"\u00a2\u0006\u0002\u0010(J*\u0010)\u001a\u00020\u00102\u0006\u0010\f\u001a\u00020\u00072\u0006\u0010\u000e\u001a\u00020\u00072\u0012\u0010*\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\b0+R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R&\u0010\u0005\u001a\u001a\u0012\u0004\u0012\u00020\u0007\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\b0\u00060\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006-"}, d2 = {"Lcom/example/castapp/model/RemoteWindowConfigManager;", "", "()V", "gson", "Lcom/google/gson/Gson;", "receiverConfigs", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lcom/example/castapp/model/RemoteWindowConfig;", "getBatchSyncData", "", "", "receiverId", "getTextWindowConfig", "connectionId", "removeWindowConfig", "", "saveToStorage", "context", "Landroid/content/Context;", "syncVisualizationParams", "visualizationDataList", "Lcom/example/castapp/model/WindowVisualizationData;", "updateFromReceiverData", "windowInfoList", "Lcom/example/castapp/model/CastWindowInfo;", "updateTextContent", "textContent", "richTextData", "updateTextFormat", "isBold", "", "isItalic", "fontSize", "", "fontName", "fontFamily", "lineSpacing", "", "textAlignment", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Float;Ljava/lang/Integer;)V", "updateWindowConfig", "updateAction", "Lkotlin/Function1;", "Companion", "app_debug"})
public final class RemoteWindowConfigManager {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.model.RemoteWindowConfigManager INSTANCE;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREFS_NAME = "remote_window_config";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_PREFIX = "config_";
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.util.concurrent.ConcurrentHashMap<java.lang.String, com.example.castapp.model.RemoteWindowConfig>> receiverConfigs = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.model.RemoteWindowConfigManager.Companion Companion = null;
    
    private RemoteWindowConfigManager() {
        super();
    }
    
    /**
     * 🎯 从接收端数据更新窗口配置（主要数据源）
     */
    public final void updateFromReceiverData(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.CastWindowInfo> windowInfoList, @org.jetbrains.annotations.Nullable()
    android.content.Context context) {
    }
    
    /**
     * 🎯 从可视化组件同步实时参数
     */
    public final void syncVisualizationParams(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.WindowVisualizationData> visualizationDataList) {
    }
    
    /**
     * 🎯 更新单个窗口参数
     */
    public final void updateWindowConfig(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.castapp.model.RemoteWindowConfig, com.example.castapp.model.RemoteWindowConfig> updateAction) {
    }
    
    /**
     * 🎯 获取批量同步数据
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.util.Map<java.lang.String, java.lang.Object>> getBatchSyncData(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
        return null;
    }
    
    /**
     * 🎯 新增：更新文字窗口内容
     */
    public final void updateTextContent(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    java.lang.String textContent, @org.jetbrains.annotations.Nullable()
    java.lang.String richTextData) {
    }
    
    /**
     * 🎯 新增：更新文字窗口格式
     */
    public final void updateTextFormat(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isBold, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isItalic, @org.jetbrains.annotations.Nullable()
    java.lang.Integer fontSize, @org.jetbrains.annotations.Nullable()
    java.lang.String fontName, @org.jetbrains.annotations.Nullable()
    java.lang.String fontFamily, @org.jetbrains.annotations.Nullable()
    java.lang.Float lineSpacing, @org.jetbrains.annotations.Nullable()
    java.lang.Integer textAlignment) {
    }
    
    /**
     * 🎯 新增：获取文字窗口配置
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.model.RemoteWindowConfig getTextWindowConfig(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 🗑️ 删除单个窗口配置
     */
    public final void removeWindowConfig(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 🎯 保存配置到本地存储
     */
    public final void saveToStorage(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\b\u001a\u00020\u0004R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\t"}, d2 = {"Lcom/example/castapp/model/RemoteWindowConfigManager$Companion;", "", "()V", "INSTANCE", "Lcom/example/castapp/model/RemoteWindowConfigManager;", "KEY_PREFIX", "", "PREFS_NAME", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.model.RemoteWindowConfigManager getInstance() {
            return null;
        }
    }
}