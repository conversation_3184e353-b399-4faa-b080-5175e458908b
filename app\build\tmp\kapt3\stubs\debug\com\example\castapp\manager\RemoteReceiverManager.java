package com.example.castapp.manager;

/**
 * 🐾 接收端管理器
 * 负责处理所有接收端相关的业务逻辑，包括连接管理、消息处理和控制对话框管理
 *
 * 主要功能：
 * - 接收端设备的连接和断开
 * - 接收端消息的处理和分发
 * - 接收端控制对话框的管理
 * - 与 RemoteConnectionManager 协作进行状态管理
 *
 * 🔧 修复：改为单例模式，确保全局唯一实例
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000x\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010%\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u001c\n\u0002\u0010$\n\u0002\b\u0013\u0018\u0000 [2\u00020\u0001:\u0001[B\u0011\b\u0002\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001e\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00072\u0006\u0010\u0018\u001a\u00020\u00072\u0006\u0010\u0019\u001a\u00020\u0012J\u000e\u0010\u001a\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0007J4\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020 2\u001c\b\u0002\u0010!\u001a\u0016\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u0016\u0018\u00010\"J4\u0010#\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010\u001f\u001a\u00020 2\u001c\b\u0002\u0010!\u001a\u0016\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u0016\u0018\u00010\"JF\u0010$\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010%\u001a\u00020\u001c2\b\b\u0002\u0010&\u001a\u00020\u00072\u0006\u0010\u001f\u001a\u00020 2\u001c\b\u0002\u0010!\u001a\u0016\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u0016\u0018\u00010\"J\u0010\u0010\'\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0017\u001a\u00020\u0007J\u001c\u0010(\u001a\u0010\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u0010\u0018\u00010\u000f2\u0006\u0010\u0017\u001a\u00020\u0007J\u0018\u0010)\u001a\u0004\u0018\u00010\u00122\u0006\u0010\u0017\u001a\u00020\u00072\u0006\u0010\u0018\u001a\u00020\u0007J\u0018\u0010*\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,H\u0002J4\u0010-\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,2\u001a\u0010!\u001a\u0016\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u0016\u0018\u00010\"H\u0002J,\u0010.\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u001a\u0010!\u001a\u0016\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u0016\u0018\u00010\"H\u0002J<\u0010/\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u00100\u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020 2\u001a\u0010!\u001a\u0016\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u0016\u0018\u00010\"H\u0002J\u0018\u00101\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,H\u0002J\u0018\u00102\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,H\u0002J\u0018\u00103\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,H\u0002J\u0018\u00104\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,H\u0002J4\u00105\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,2\u001a\u0010!\u001a\u0016\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u0016\u0018\u00010\"H\u0002J4\u00106\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,2\u001a\u0010!\u001a\u0016\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u0016\u0018\u00010\"H\u0002J\u0018\u00107\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,H\u0002J4\u00108\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,2\u001a\u0010!\u001a\u0016\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u0016\u0018\u00010\"H\u0002J4\u00109\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,2\u001a\u0010!\u001a\u0016\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u0016\u0018\u00010\"H\u0002J\u0018\u0010:\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,H\u0002J\u0018\u0010;\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,H\u0002J\u0018\u0010<\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,H\u0002J\u0018\u0010=\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,H\u0002J\u0018\u0010>\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,H\u0002J\u000e\u0010?\u001a\u00020\u001c2\u0006\u0010\u0017\u001a\u00020\u0007J\u0010\u0010@\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001eH\u0002J\u0018\u0010A\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00072\u0006\u0010+\u001a\u00020,H\u0002J\u0018\u0010B\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00072\u0006\u0010C\u001a\u00020\u0007H\u0002J\u0018\u0010D\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00072\u0006\u0010+\u001a\u00020,H\u0002J\u0018\u0010E\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00072\u0006\u0010C\u001a\u00020\u0007H\u0002J\u0010\u0010F\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0007H\u0002J$\u0010G\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00072\u0012\u0010H\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010IH\u0002J\u0018\u0010J\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00072\u0006\u0010+\u001a\u00020,H\u0002J\u0016\u0010K\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00072\u0006\u0010L\u001a\u00020\tJ\u0016\u0010M\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00072\u0006\u0010L\u001a\u00020\u000bJ\u0016\u0010N\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00072\u0006\u0010L\u001a\u00020\rJ\u0016\u0010O\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00072\u0006\u0010\u0018\u001a\u00020\u0007J\u0016\u0010P\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,J\u000e\u0010Q\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001eJ\u000e\u0010R\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001eJ\"\u0010S\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0012\u0010T\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010IJ\u000e\u0010U\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001eJ\u0016\u0010V\u001a\u00020\u00162\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u0010+\u001a\u00020,J\u0018\u0010W\u001a\u00020\u00162\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010+\u001a\u00020\u0007H\u0002J\u000e\u0010X\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0007J\u000e\u0010Y\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0007J\u000e\u0010Z\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0007R \u0010\u0005\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\n\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R \u0010\f\u001a\u0014\u0012\u0004\u0012\u00020\u0007\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R&\u0010\u000e\u001a\u001a\u0012\u0004\u0012\u00020\u0007\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00100\u000f0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R&\u0010\u0011\u001a\u001a\u0012\u0004\u0012\u00020\u0007\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00120\u00060\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\\"}, d2 = {"Lcom/example/castapp/manager/RemoteReceiverManager;", "", "connectionManager", "Lcom/example/castapp/manager/RemoteConnectionManager;", "(Lcom/example/castapp/manager/RemoteConnectionManager;)V", "activeReceiverControlDialogs", "", "", "Ljava/lang/ref/WeakReference;", "Lcom/example/castapp/ui/dialog/RemoteReceiverControlDialog;", "activeReceiverSettingsControlDialogs", "Lcom/example/castapp/ui/dialog/RemoteReceiverSettingsControlDialog;", "activeWindowManagerDialogs", "Lcom/example/castapp/ui/dialog/RemoteWindowManagerDialog;", "cachedScreenResolutions", "Lkotlin/Pair;", "", "globalActiveRemoteTextWindowManagers", "Lcom/example/castapp/ui/windowsettings/RemoteTextWindowManager;", "uiHandler", "Landroid/os/Handler;", "addGlobalRemoteTextWindowManager", "", "receiverId", "connectionId", "manager", "clearGlobalRemoteTextWindowManagers", "connectReceiver", "", "receiver", "Lcom/example/castapp/model/RemoteReceiverConnection;", "context", "Landroid/content/Context;", "onConnectionStateChanged", "Lkotlin/Function2;", "disconnectReceiver", "disconnectReceiverWithType", "sendMessage", "reason", "getActiveControlDialog", "getCachedScreenResolution", "getGlobalRemoteTextWindowManager", "handleCameraWindowCreatedNotification", "message", "Lcom/example/castapp/websocket/ControlMessage;", "handleConnectionResponse", "handleDisconnectMessage", "handleReceiverConnectionStateChanged", "isConnected", "handleReceiverLocalAudioVideoChanged", "handleReceiverLocalPlaybackModeChanged", "handleReceiverLocalSettingsBroadcast", "handleReceiverLocalVolumeChanged", "handleReceiverMessage", "handleRemoteControlServiceStopped", "handleRemoteReceiverSettingsSync", "handleScreenResolution", "handleScreenResolutionResponse", "handleScreenshotError", "handleScreenshotResponse", "handleTextContentError", "handleTextContentResponse", "handleWindowManagerResponse", "isSyncEnabledForReceiver", "notifyReceiverControlDialogs", "notifyReceiverControlDialogsScreenshot", "notifyReceiverControlDialogsScreenshotError", "errorMessage", "notifyReceiverControlDialogsTextContent", "notifyReceiverControlDialogsTextContentError", "notifyReceiverControlDialogsTriggerUpdate", "notifySettingsControlDialog", "settings", "", "notifyWindowManagerDialogs", "registerReceiverControlDialog", "dialog", "registerReceiverSettingsControlDialog", "registerWindowManagerDialog", "removeGlobalRemoteTextWindowManager", "sendNoteUpdate", "sendScreenshotRequest", "sendTextContentRequest", "sendTextFormatSyncMessage", "formatData", "sendWindowManagerRequest", "sendWindowTransformControl", "showToastOnUiThread", "unregisterReceiverControlDialog", "unregisterReceiverSettingsControlDialog", "unregisterWindowManagerDialog", "Companion", "app_debug"})
public final class RemoteReceiverManager {
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.manager.RemoteConnectionManager connectionManager = null;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.castapp.manager.RemoteReceiverManager INSTANCE;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, java.lang.ref.WeakReference<com.example.castapp.ui.dialog.RemoteReceiverControlDialog>> activeReceiverControlDialogs = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, java.lang.ref.WeakReference<com.example.castapp.ui.dialog.RemoteReceiverSettingsControlDialog>> activeReceiverSettingsControlDialogs = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, kotlin.Pair<java.lang.Integer, java.lang.Integer>> cachedScreenResolutions = null;
    @org.jetbrains.annotations.NotNull()
    private final android.os.Handler uiHandler = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, java.lang.ref.WeakReference<com.example.castapp.ui.dialog.RemoteWindowManagerDialog>> activeWindowManagerDialogs = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, java.util.Map<java.lang.String, com.example.castapp.ui.windowsettings.RemoteTextWindowManager>> globalActiveRemoteTextWindowManagers = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.manager.RemoteReceiverManager.Companion Companion = null;
    
    private RemoteReceiverManager(com.example.castapp.manager.RemoteConnectionManager connectionManager) {
        super();
    }
    
    /**
     * 连接到接收端设备
     */
    public final boolean connectReceiver(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection receiver, @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super com.example.castapp.model.RemoteReceiverConnection, ? super java.lang.Boolean, kotlin.Unit> onConnectionStateChanged) {
        return false;
    }
    
    /**
     * 断开接收端连接
     */
    public final void disconnectReceiver(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection receiver, @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super com.example.castapp.model.RemoteReceiverConnection, ? super java.lang.Boolean, kotlin.Unit> onConnectionStateChanged) {
    }
    
    /**
     * 断开接收端连接（带类型）
     */
    public final void disconnectReceiverWithType(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection receiver, boolean sendMessage, @org.jetbrains.annotations.NotNull()
    java.lang.String reason, @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function2<? super com.example.castapp.model.RemoteReceiverConnection, ? super java.lang.Boolean, kotlin.Unit> onConnectionStateChanged) {
    }
    
    /**
     * 注册接收端控制对话框，用于接收连接状态变化通知
     */
    public final void registerReceiverControlDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.dialog.RemoteReceiverControlDialog dialog) {
    }
    
    /**
     * 注销接收端控制对话框
     */
    public final void unregisterReceiverControlDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
    }
    
    /**
     * 处理接收端连接状态变化
     */
    private final void handleReceiverConnectionStateChanged(com.example.castapp.model.RemoteReceiverConnection receiver, boolean isConnected, android.content.Context context, kotlin.jvm.functions.Function2<? super com.example.castapp.model.RemoteReceiverConnection, ? super java.lang.Boolean, kotlin.Unit> onConnectionStateChanged) {
    }
    
    /**
     * 通知所有相关的控制对话框连接状态变化
     */
    private final void notifyReceiverControlDialogs(com.example.castapp.model.RemoteReceiverConnection receiver) {
    }
    
    /**
     * 🚀 架构修复：处理接收端设备消息（仅处理遥控管理相关消息）
     * 发送端音视频控制消息由 MessageReceivingManager 处理，避免功能混淆
     */
    private final void handleReceiverMessage(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message, kotlin.jvm.functions.Function2<? super com.example.castapp.model.RemoteReceiverConnection, ? super java.lang.Boolean, kotlin.Unit> onConnectionStateChanged) {
    }
    
    /**
     * 处理连接响应消息
     */
    private final void handleConnectionResponse(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message, kotlin.jvm.functions.Function2<? super com.example.castapp.model.RemoteReceiverConnection, ? super java.lang.Boolean, kotlin.Unit> onConnectionStateChanged) {
    }
    
    /**
     * 处理屏幕分辨率消息
     */
    private final void handleScreenResolution(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message, kotlin.jvm.functions.Function2<? super com.example.castapp.model.RemoteReceiverConnection, ? super java.lang.Boolean, kotlin.Unit> onConnectionStateChanged) {
    }
    
    /**
     * 处理远程被控服务停止消息
     */
    private final void handleRemoteControlServiceStopped(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message, kotlin.jvm.functions.Function2<? super com.example.castapp.model.RemoteReceiverConnection, ? super java.lang.Boolean, kotlin.Unit> onConnectionStateChanged) {
    }
    
    /**
     * 处理断开连接消息
     */
    private final void handleDisconnectMessage(com.example.castapp.model.RemoteReceiverConnection receiver, kotlin.jvm.functions.Function2<? super com.example.castapp.model.RemoteReceiverConnection, ? super java.lang.Boolean, kotlin.Unit> onConnectionStateChanged) {
    }
    
    /**
     * 🐾 在UI线程安全地显示Toast
     */
    private final void showToastOnUiThread(android.content.Context context, java.lang.String message) {
    }
    
    /**
     * 注册远程接收端设置控制对话框
     */
    public final void registerReceiverSettingsControlDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.dialog.RemoteReceiverSettingsControlDialog dialog) {
    }
    
    /**
     * 注销远程接收端设置控制对话框
     */
    public final void unregisterReceiverSettingsControlDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
    }
    
    /**
     * 处理远程接收端设置同步消息
     */
    private final void handleRemoteReceiverSettingsSync(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 处理接收端本地音视频服务状态变更
     */
    private final void handleReceiverLocalAudioVideoChanged(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 处理接收端本地播放模式变更
     */
    private final void handleReceiverLocalPlaybackModeChanged(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 处理接收端本地音量变更
     */
    private final void handleReceiverLocalVolumeChanged(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 处理接收端本地设置广播
     */
    private final void handleReceiverLocalSettingsBroadcast(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 通知设置控制对话框更新
     */
    private final void notifySettingsControlDialog(java.lang.String receiverId, java.util.Map<java.lang.String, ? extends java.lang.Object> settings) {
    }
    
    /**
     * 🪟 发送投屏窗口管理请求
     */
    public final void sendWindowManagerRequest(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection receiver) {
    }
    
    /**
     * 🔄 发送窗口变换控制消息
     */
    public final void sendWindowTransformControl(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection receiver, @org.jetbrains.annotations.NotNull()
    com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 🏷️ 发送备注更新消息
     */
    public final void sendNoteUpdate(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection receiver, @org.jetbrains.annotations.NotNull()
    com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 🪟 处理投屏窗口管理响应消息
     */
    private final void handleWindowManagerResponse(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 🪟 注册远程窗口管理对话框
     */
    public final void registerWindowManagerDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.dialog.RemoteWindowManagerDialog dialog) {
    }
    
    /**
     * 🪟 注销远程窗口管理对话框
     */
    public final void unregisterWindowManagerDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
    }
    
    /**
     * 🪟 通知窗口管理对话框更新
     */
    private final void notifyWindowManagerDialogs(java.lang.String receiverId, com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 🎯 添加全局遥控端文字窗口管理器
     */
    public final void addGlobalRemoteTextWindowManager(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    com.example.castapp.ui.windowsettings.RemoteTextWindowManager manager) {
    }
    
    /**
     * 🎯 获取全局遥控端文字窗口管理器
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.windowsettings.RemoteTextWindowManager getGlobalRemoteTextWindowManager(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return null;
    }
    
    /**
     * 🎯 移除全局遥控端文字窗口管理器
     */
    public final void removeGlobalRemoteTextWindowManager(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
    }
    
    /**
     * 🎯 清理接收端的所有遥控端文字窗口管理器
     */
    public final void clearGlobalRemoteTextWindowManagers(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
    }
    
    /**
     * 📸 发送截图请求
     */
    public final void sendScreenshotRequest(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection receiver) {
    }
    
    /**
     * 📝 发送文字内容请求
     */
    public final void sendTextContentRequest(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection receiver) {
    }
    
    /**
     * 📝 发送文字格式同步消息
     */
    public final void sendTextFormatSyncMessage(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.RemoteReceiverConnection receiver, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
    }
    
    /**
     * 📝 检查接收端的同步开关状态
     */
    public final boolean isSyncEnabledForReceiver(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
        return false;
    }
    
    /**
     * 📝 获取活跃的控制对话框
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.dialog.RemoteReceiverControlDialog getActiveControlDialog(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
        return null;
    }
    
    /**
     * 📸 处理截图响应消息
     */
    private final void handleScreenshotResponse(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 📸 处理截图错误消息
     */
    private final void handleScreenshotError(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 📸 通知远程控制对话框显示截图
     */
    private final void notifyReceiverControlDialogsScreenshot(java.lang.String receiverId, com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 📸 通知远程控制对话框显示截图错误
     */
    private final void notifyReceiverControlDialogsScreenshotError(java.lang.String receiverId, java.lang.String errorMessage) {
    }
    
    /**
     * 📝 处理文字内容响应消息
     */
    private final void handleTextContentResponse(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 📝 处理文字内容错误消息
     */
    private final void handleTextContentError(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 📝 通知远程控制对话框显示文字内容
     */
    private final void notifyReceiverControlDialogsTextContent(java.lang.String receiverId, com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 📝 通知远程控制对话框显示文字内容错误
     */
    private final void notifyReceiverControlDialogsTextContentError(java.lang.String receiverId, java.lang.String errorMessage) {
    }
    
    /**
     * 📐 获取缓存的屏幕分辨率信息
     */
    @org.jetbrains.annotations.Nullable()
    public final kotlin.Pair<java.lang.Integer, java.lang.Integer> getCachedScreenResolution(@org.jetbrains.annotations.NotNull()
    java.lang.String receiverId) {
        return null;
    }
    
    /**
     * 📐 处理屏幕分辨率响应消息
     */
    private final void handleScreenResolutionResponse(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message, kotlin.jvm.functions.Function2<? super com.example.castapp.model.RemoteReceiverConnection, ? super java.lang.Boolean, kotlin.Unit> onConnectionStateChanged) {
    }
    
    /**
     * 📱 处理摄像头窗口创建通知消息
     */
    private final void handleCameraWindowCreatedNotification(com.example.castapp.model.RemoteReceiverConnection receiver, com.example.castapp.websocket.ControlMessage message) {
    }
    
    /**
     * 📱 通知远程控制对话框触发更新功能
     */
    private final void notifyReceiverControlDialogsTriggerUpdate(java.lang.String receiverId) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0005\u001a\u00020\u0004R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/castapp/manager/RemoteReceiverManager$Companion;", "", "()V", "INSTANCE", "Lcom/example/castapp/manager/RemoteReceiverManager;", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.manager.RemoteReceiverManager getInstance() {
            return null;
        }
    }
}