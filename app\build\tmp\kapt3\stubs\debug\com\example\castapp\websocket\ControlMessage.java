package com.example.castapp.websocket;

/**
 * WebSocket控制消息数据类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\u0012\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0007\b\u0086\b\u0018\u0000 #2\u00020\u0001:\u0001#B+\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0014\b\u0002\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0006\u00a2\u0006\u0002\u0010\u0007J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\u0015\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0006H\u00c6\u0003J3\u0010\u0010\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\u0014\b\u0002\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0006H\u00c6\u0001J\u0013\u0010\u0011\u001a\u00020\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0015\u0010\u0014\u001a\u0004\u0018\u00010\u00122\u0006\u0010\u0015\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0016J\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u00182\u0006\u0010\u0015\u001a\u00020\u0003J\u0015\u0010\u0019\u001a\u0004\u0018\u00010\u001a2\u0006\u0010\u0015\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u001bJ\u0015\u0010\u001c\u001a\u0004\u0018\u00010\u001d2\u0006\u0010\u0015\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u001eJ\u0010\u0010\u001f\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0015\u001a\u00020\u0003J\t\u0010 \u001a\u00020\u001aH\u00d6\u0001J\u0006\u0010!\u001a\u00020\u0003J\t\u0010\"\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u001d\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\t\u00a8\u0006$"}, d2 = {"Lcom/example/castapp/websocket/ControlMessage;", "", "type", "", "connectionId", "data", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;)V", "getConnectionId", "()Ljava/lang/String;", "getData", "()Ljava/util/Map;", "getType", "component1", "component2", "component3", "copy", "equals", "", "other", "getBooleanData", "key", "(Ljava/lang/String;)Ljava/lang/Boolean;", "getByteArrayData", "", "getIntData", "", "(Ljava/lang/String;)Ljava/lang/Integer;", "getLongData", "", "(Ljava/lang/String;)Ljava/lang/Long;", "getStringData", "hashCode", "toJson", "toString", "Companion", "app_debug"})
public final class ControlMessage {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String type = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String connectionId = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, java.lang.Object> data = null;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_CONNECTION_REQUEST = "connection_request";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_CONNECTION_RESPONSE = "connection_response";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_SCREEN_RESOLUTION = "screen_resolution";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_DISCONNECT = "disconnect";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_HEARTBEAT = "heartbeat";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_H264_CONFIG = "h264_config";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_SSRC_MAPPING = "ssrc_mapping";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_CASTING_STATE = "casting_state";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_BITRATE_CONTROL = "bitrate_control";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_RESOLUTION_CHANGE = "resolution_change";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_RESOLUTION_ADJUSTMENT_COMPLETE = "resolution_adjustment_complete";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_MEDIA_AUDIO_CONTROL = "media_audio_control";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_MIC_AUDIO_CONTROL = "mic_audio_control";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_AAC_CONFIG = "aac_config";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_VIDEO_STREAM_STOP = "video_stream_stop";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_FUNCTION_CONTROL = "function_control";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_BITRATE_CHANGE = "remote_bitrate_change";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_RESOLUTION_CHANGE = "remote_resolution_change";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_VOLUME_CHANGE = "remote_volume_change";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_CONNECTION_TOGGLE = "remote_connection_toggle";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_SETTINGS_SYNC = "remote_settings_sync";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_CONTROL_REQUEST = "remote_control_request";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_CONTROL_RESPONSE = "remote_control_response";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_CONNECTION_LIST_SYNC = "remote_connection_list_sync";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_CONNECTION_ADDED = "remote_connection_added";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_CONNECTION_UPDATED = "remote_connection_updated";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_CONNECTION_REMOVED = "remote_connection_removed";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_CONTROL_SERVICE_STOPPED = "remote_control_service_stopped";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_CONNECTION_ADD_REQUEST = "remote_connection_add_request";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_CONNECTION_EDIT_REQUEST = "remote_connection_edit_request";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_CONNECTION_DELETE_REQUEST = "remote_connection_delete_request";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_RECEIVER_AUDIO_VIDEO_TOGGLE = "remote_receiver_audio_video_toggle";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_RECEIVER_PLAYBACK_MODE_CHANGE = "remote_receiver_playback_mode_change";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_RECEIVER_VOLUME_CHANGE = "remote_receiver_volume_change";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_RECEIVER_SETTINGS_SYNC = "remote_receiver_settings_sync";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_RECEIVER_SETTINGS_REQUEST = "remote_receiver_settings_request";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_RECEIVER_LOCAL_AUDIO_VIDEO_CHANGED = "receiver_local_audio_video_changed";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_RECEIVER_LOCAL_PLAYBACK_MODE_CHANGED = "receiver_local_playback_mode_changed";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_RECEIVER_LOCAL_VOLUME_CHANGED = "receiver_local_volume_changed";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_RECEIVER_LOCAL_SETTINGS_BROADCAST = "receiver_local_settings_broadcast";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_WINDOW_MANAGER_REQUEST = "window_manager_request";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_WINDOW_MANAGER_RESPONSE = "window_manager_response";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_SCREENSHOT_REQUEST = "screenshot_request";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_SCREENSHOT_RESPONSE = "screenshot_response";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_SCREENSHOT_ERROR = "screenshot_error";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_TEXT_CONTENT_REQUEST = "text_content_request";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_TEXT_CONTENT_RESPONSE = "text_content_response";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_TEXT_CONTENT_ERROR = "text_content_error";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_TEXT_FORMAT_SYNC = "text_format_sync";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_SCREEN_RESOLUTION_REQUEST = "screen_resolution_request";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_SCREEN_RESOLUTION_RESPONSE = "screen_resolution_response";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_LANDSCAPE_MODE_CONTROL = "landscape_mode_control";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_REMOTE_WINDOW_TRANSFORM_CONTROL = "remote_window_transform_control";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String TYPE_NOTE_UPDATE = "note_update";
    @org.jetbrains.annotations.NotNull()
    private static final com.google.gson.Gson gson = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.websocket.ControlMessage.Companion Companion = null;
    
    public ControlMessage(@org.jetbrains.annotations.NotNull()
    java.lang.String type, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> data) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getConnectionId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getData() {
        return null;
    }
    
    /**
     * 获取布尔类型数据
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean getBooleanData(@org.jetbrains.annotations.NotNull()
    java.lang.String key) {
        return null;
    }
    
    /**
     * 获取字符串数据
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getStringData(@org.jetbrains.annotations.NotNull()
    java.lang.String key) {
        return null;
    }
    
    /**
     * 获取整数数据
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getIntData(@org.jetbrains.annotations.NotNull()
    java.lang.String key) {
        return null;
    }
    
    /**
     * 获取Long数据
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getLongData(@org.jetbrains.annotations.NotNull()
    java.lang.String key) {
        return null;
    }
    
    /**
     * 获取字节数组数据
     */
    @org.jetbrains.annotations.Nullable()
    public final byte[] getByteArrayData(@org.jetbrains.annotations.NotNull()
    java.lang.String key) {
        return null;
    }
    
    /**
     * 转换为JSON字符串
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String toJson() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.websocket.ControlMessage copy(@org.jetbrains.annotations.NotNull()
    java.lang.String type, @org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, ? extends java.lang.Object> data) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b6\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0012\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010$\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0013\n\u0002\u0010%\n\u0002\b=\n\u0002\u0010\t\n\u0002\b\r\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010<\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\b\u0010?\u001a\u0004\u0018\u00010@J(\u0010A\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0018\u0010B\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010D0CJ\"\u0010E\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010F\u001a\u00020G2\n\b\u0002\u0010H\u001a\u0004\u0018\u00010\u0004J \u0010I\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010J\u001a\u00020K2\b\b\u0002\u0010L\u001a\u00020\u0004J\u001a\u0010M\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\n\b\u0002\u0010N\u001a\u0004\u0018\u00010\u0004J \u0010O\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010P\u001a\u00020K2\b\b\u0002\u0010Q\u001a\u00020\u0004J\u000e\u0010R\u001a\u00020=2\u0006\u0010>\u001a\u00020\u0004J\u001e\u0010S\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010T\u001a\u00020\u00042\u0006\u0010U\u001a\u00020KJ\"\u0010V\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\b\u0010W\u001a\u0004\u0018\u00010@2\b\u0010X\u001a\u0004\u0018\u00010@J2\u0010Y\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\b\u0010W\u001a\u0004\u0018\u00010@2\b\u0010X\u001a\u0004\u0018\u00010@2\u0006\u0010Z\u001a\u00020G2\u0006\u0010[\u001a\u00020GJ[\u0010\\\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\b\u0010W\u001a\u0004\u0018\u00010@2\b\u0010X\u001a\u0004\u0018\u00010@2\u0006\u0010Z\u001a\u00020G2\u0006\u0010[\u001a\u00020G2\n\b\u0002\u0010]\u001a\u0004\u0018\u00010G2\u0016\b\u0002\u0010^\u001a\u0010\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u0001\u0018\u00010_\u00a2\u0006\u0002\u0010`J\u000e\u0010a\u001a\u00020=2\u0006\u0010>\u001a\u00020\u0004J\u0016\u0010b\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010U\u001a\u00020KJ\u0016\u0010c\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010d\u001a\u00020KJ\u0016\u0010e\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010d\u001a\u00020KJ\u001e\u0010f\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010g\u001a\u00020\u00042\u0006\u0010h\u001a\u00020\u0004J\u0016\u0010i\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010U\u001a\u00020KJ\u0016\u0010j\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010k\u001a\u00020KJ\u0016\u0010l\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010m\u001a\u00020GJ\u0016\u0010n\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010o\u001a\u00020GJ\u001e\u0010p\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010q\u001a\u00020\u00042\u0006\u0010r\u001a\u00020GJ\"\u0010s\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0012\u0010t\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010DJ\u0016\u0010u\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010H\u001a\u00020\u0004J&\u0010v\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010H\u001a\u00020\u00042\u0006\u0010w\u001a\u00020\u00042\u0006\u0010x\u001a\u00020GJ\u0016\u0010y\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010z\u001a\u00020\u0004J*\u0010{\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010T\u001a\u00020\u00042\u0006\u0010U\u001a\u00020K2\n\b\u0002\u0010H\u001a\u0004\u0018\u00010\u0004J\"\u0010|\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0012\u0010t\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010DJ\u0016\u0010}\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010N\u001a\u00020\u0004J \u0010~\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010P\u001a\u00020K2\b\b\u0002\u0010Q\u001a\u00020\u0004J\u0018\u0010\u007f\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\b\b\u0002\u0010L\u001a\u00020\u0004J*\u0010\u0080\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0019\u0010\u0081\u0001\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010D0CJ\u0017\u0010\u0082\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010U\u001a\u00020KJ\u0017\u0010\u0083\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010k\u001a\u00020KJ\u000f\u0010\u0084\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u0004J$\u0010\u0085\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0013\u0010\u0086\u0001\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010DJ\u0017\u0010\u0087\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010m\u001a\u00020GJ\u0018\u0010\u0088\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0007\u0010\u0089\u0001\u001a\u00020GJ$\u0010\u008a\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0013\u0010\u0086\u0001\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010DJ \u0010\u008b\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0007\u0010\u008c\u0001\u001a\u00020\u00042\u0006\u0010m\u001a\u00020GJ5\u0010\u008d\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010g\u001a\u00020\u00042\u0007\u0010\u008e\u0001\u001a\u00020\u00042\u0013\u0010\u008f\u0001\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010DJ4\u0010\u0090\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010Z\u001a\u00020G2\u0006\u0010[\u001a\u00020G2\u0006\u0010P\u001a\u00020K2\u000b\b\u0002\u0010\u0091\u0001\u001a\u0004\u0018\u00010\u0004J\u001f\u0010\u0092\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010Z\u001a\u00020G2\u0006\u0010[\u001a\u00020GJ\u001f\u0010\u0093\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010Z\u001a\u00020G2\u0006\u0010[\u001a\u00020GJ\u000f\u0010\u0094\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u0004J\u001f\u0010\u0095\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0006\u0010Z\u001a\u00020G2\u0006\u0010[\u001a\u00020GJ\u0018\u0010\u0096\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0007\u0010\u0097\u0001\u001a\u00020\u0004J\u000f\u0010\u0098\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u0004J*\u0010\u0099\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0019\u0010\u009a\u0001\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010D0CJ\u0019\u0010\u009b\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\b\u0010\u009c\u0001\u001a\u00030\u009d\u0001J\u0018\u0010\u009e\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0007\u0010\u0097\u0001\u001a\u00020\u0004J\u000f\u0010\u009f\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u0004J*\u0010\u00a0\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0019\u0010\u00a1\u0001\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010D0CJ$\u0010\u00a2\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0013\u0010\u00a3\u0001\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010DJ\u000f\u0010\u00a4\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u0004J\u000f\u0010\u00a5\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u0004J*\u0010\u00a6\u0001\u001a\u00020=2\u0006\u0010>\u001a\u00020\u00042\u0019\u0010\u00a7\u0001\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010D0CJ\u0012\u0010\u00a8\u0001\u001a\u0004\u0018\u00010=2\u0007\u0010\u00a9\u0001\u001a\u00020\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010!\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010%\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\'\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010)\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010,\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010.\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00100\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00101\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00102\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00103\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00104\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00105\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00106\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00107\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00108\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u00109\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010:\u001a\u00020;X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u00aa\u0001"}, d2 = {"Lcom/example/castapp/websocket/ControlMessage$Companion;", "", "()V", "TYPE_AAC_CONFIG", "", "TYPE_BITRATE_CONTROL", "TYPE_CASTING_STATE", "TYPE_CONNECTION_REQUEST", "TYPE_CONNECTION_RESPONSE", "TYPE_DISCONNECT", "TYPE_FUNCTION_CONTROL", "TYPE_H264_CONFIG", "TYPE_HEARTBEAT", "TYPE_LANDSCAPE_MODE_CONTROL", "TYPE_MEDIA_AUDIO_CONTROL", "TYPE_MIC_AUDIO_CONTROL", "TYPE_NOTE_UPDATE", "TYPE_RECEIVER_LOCAL_AUDIO_VIDEO_CHANGED", "TYPE_RECEIVER_LOCAL_PLAYBACK_MODE_CHANGED", "TYPE_RECEIVER_LOCAL_SETTINGS_BROADCAST", "TYPE_RECEIVER_LOCAL_VOLUME_CHANGED", "TYPE_REMOTE_BITRATE_CHANGE", "TYPE_REMOTE_CONNECTION_ADDED", "TYPE_REMOTE_CONNECTION_ADD_REQUEST", "TYPE_REMOTE_CONNECTION_DELETE_REQUEST", "TYPE_REMOTE_CONNECTION_EDIT_REQUEST", "TYPE_REMOTE_CONNECTION_LIST_SYNC", "TYPE_REMOTE_CONNECTION_REMOVED", "TYPE_REMOTE_CONNECTION_TOGGLE", "TYPE_REMOTE_CONNECTION_UPDATED", "TYPE_REMOTE_CONTROL_REQUEST", "TYPE_REMOTE_CONTROL_RESPONSE", "TYPE_REMOTE_CONTROL_SERVICE_STOPPED", "TYPE_REMOTE_RECEIVER_AUDIO_VIDEO_TOGGLE", "TYPE_REMOTE_RECEIVER_PLAYBACK_MODE_CHANGE", "TYPE_REMOTE_RECEIVER_SETTINGS_REQUEST", "TYPE_REMOTE_RECEIVER_SETTINGS_SYNC", "TYPE_REMOTE_RECEIVER_VOLUME_CHANGE", "TYPE_REMOTE_RESOLUTION_CHANGE", "TYPE_REMOTE_SETTINGS_SYNC", "TYPE_REMOTE_VOLUME_CHANGE", "TYPE_REMOTE_WINDOW_TRANSFORM_CONTROL", "TYPE_RESOLUTION_ADJUSTMENT_COMPLETE", "TYPE_RESOLUTION_CHANGE", "TYPE_SCREENSHOT_ERROR", "TYPE_SCREENSHOT_REQUEST", "TYPE_SCREENSHOT_RESPONSE", "TYPE_SCREEN_RESOLUTION", "TYPE_SCREEN_RESOLUTION_REQUEST", "TYPE_SCREEN_RESOLUTION_RESPONSE", "TYPE_SSRC_MAPPING", "TYPE_TEXT_CONTENT_ERROR", "TYPE_TEXT_CONTENT_REQUEST", "TYPE_TEXT_CONTENT_RESPONSE", "TYPE_TEXT_FORMAT_SYNC", "TYPE_VIDEO_STREAM_STOP", "TYPE_WINDOW_MANAGER_REQUEST", "TYPE_WINDOW_MANAGER_RESPONSE", "gson", "Lcom/google/gson/Gson;", "createAacConfig", "Lcom/example/castapp/websocket/ControlMessage;", "connectionId", "configData", "", "createBatchWindowSyncControl", "allWindowsData", "", "", "createBitrateControl", "bitrate", "", "targetConnectionId", "createCastingState", "isCasting", "", "reason", "createConnectionRequest", "deviceName", "createConnectionResponse", "success", "message", "createDisconnect", "createFunctionControl", "functionType", "enabled", "createH264Config", "spsData", "ppsData", "createH264ConfigWithResolution", "width", "height", "createH264ConfigWithResolutionAndOrientation", "orientation", "existingData", "", "(Ljava/lang/String;[B[BIILjava/lang/Integer;Ljava/util/Map;)Lcom/example/castapp/websocket/ControlMessage;", "createHeartbeat", "createLandscapeModeControl", "createMediaAudioControl", "isEnabled", "createMicAudioControl", "createNoteUpdate", "targetWindowId", "note", "createReceiverLocalAudioVideoChanged", "createReceiverLocalPlaybackModeChanged", "isSpeakerMode", "createReceiverLocalVolumeChanged", "volume", "createRemoteBitrateChange", "bitrateMbps", "createRemoteConnectionAddRequest", "ipAddress", "port", "createRemoteConnectionAdded", "connectionData", "createRemoteConnectionDeleteRequest", "createRemoteConnectionEditRequest", "newIpAddress", "newPort", "createRemoteConnectionRemoved", "removedConnectionId", "createRemoteConnectionToggle", "createRemoteConnectionUpdated", "createRemoteControlRequest", "createRemoteControlResponse", "createRemoteControlServiceStopped", "createRemoteLayerOrderControl", "windowOrderList", "createRemoteReceiverAudioVideoToggle", "createRemoteReceiverPlaybackModeChange", "createRemoteReceiverSettingsRequest", "createRemoteReceiverSettingsSync", "settings", "createRemoteReceiverVolumeChange", "createRemoteResolutionChange", "scalePercent", "createRemoteSettingsSync", "createRemoteVolumeChange", "volumeType", "createRemoteWindowTransformControl", "transformType", "transformData", "createResolutionAdjustmentComplete", "error", "createResolutionChange", "createScreenResolution", "createScreenResolutionRequest", "createScreenResolutionResponse", "createScreenshotError", "errorMessage", "createScreenshotRequest", "createScreenshotResponse", "screenshotDataList", "createSsrcMapping", "ssrc", "", "createTextContentError", "createTextContentRequest", "createTextContentResponse", "textContentDataList", "createTextFormatSyncMessage", "formatData", "createVideoStreamStop", "createWindowManagerRequest", "createWindowManagerResponse", "windowInfoList", "fromJson", "json", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 创建远程接收端音视频服务切换消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteReceiverAudioVideoToggle(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean enabled) {
            return null;
        }
        
        /**
         * 创建远程接收端播放模式切换消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteReceiverPlaybackModeChange(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isSpeakerMode) {
            return null;
        }
        
        /**
         * 创建远程接收端音量调整消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteReceiverVolumeChange(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int volume) {
            return null;
        }
        
        /**
         * 创建远程接收端设置同步消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteReceiverSettingsSync(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.util.Map<java.lang.String, ? extends java.lang.Object> settings) {
            return null;
        }
        
        /**
         * 创建远程接收端设置请求消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteReceiverSettingsRequest(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId) {
            return null;
        }
        
        /**
         * 创建接收端本地音视频服务状态变更消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createReceiverLocalAudioVideoChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean enabled) {
            return null;
        }
        
        /**
         * 创建接收端本地播放模式变更消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createReceiverLocalPlaybackModeChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isSpeakerMode) {
            return null;
        }
        
        /**
         * 创建接收端本地音量变更消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createReceiverLocalVolumeChanged(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int volume) {
            return null;
        }
        
        /**
         * 创建投屏窗口管理请求消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createWindowManagerRequest(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId) {
            return null;
        }
        
        /**
         * 创建投屏窗口管理响应消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createWindowManagerResponse(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> windowInfoList) {
            return null;
        }
        
        /**
         * 从JSON字符串解析控制消息
         */
        @org.jetbrains.annotations.Nullable()
        public final com.example.castapp.websocket.ControlMessage fromJson(@org.jetbrains.annotations.NotNull()
        java.lang.String json) {
            return null;
        }
        
        /**
         * 创建纯净连接请求消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createConnectionRequest(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.Nullable()
        java.lang.String deviceName) {
            return null;
        }
        
        /**
         * 创建连接响应消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createConnectionResponse(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean success, @org.jetbrains.annotations.NotNull()
        java.lang.String message) {
            return null;
        }
        
        /**
         * 创建屏幕分辨率消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createScreenResolution(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int width, int height) {
            return null;
        }
        
        /**
         * 🎯 创建横屏模式控制消息
         * 用于接收端控制发送端的横屏检测功能
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createLandscapeModeControl(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean enabled) {
            return null;
        }
        
        /**
         * 🔄 创建远程窗口变换控制消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteWindowTransformControl(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String targetWindowId, @org.jetbrains.annotations.NotNull()
        java.lang.String transformType, @org.jetbrains.annotations.NotNull()
        java.util.Map<java.lang.String, ? extends java.lang.Object> transformData) {
            return null;
        }
        
        /**
         * 🏷️ 创建备注更新消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createNoteUpdate(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String targetWindowId, @org.jetbrains.annotations.NotNull()
        java.lang.String note) {
            return null;
        }
        
        /**
         * 🎯 创建层级调整消息
         * 用于遥控端向接收端发送窗口层级调整指令
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteLayerOrderControl(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> windowOrderList) {
            return null;
        }
        
        /**
         * 🔄 创建批量窗口同步消息
         * 用于遥控端向接收端发送所有窗口的完整参数信息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createBatchWindowSyncControl(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> allWindowsData) {
            return null;
        }
        
        /**
         * 创建分辨率变化消息
         * 用于通知接收端实时分辨率调整
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createResolutionChange(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int width, int height) {
            return null;
        }
        
        /**
         * 创建分辨率调整完成消息
         * 用于接收端通知发送端分辨率调整已完成
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createResolutionAdjustmentComplete(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int width, int height, boolean success, @org.jetbrains.annotations.Nullable()
        java.lang.String error) {
            return null;
        }
        
        /**
         * 创建断开连接消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createDisconnect(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId) {
            return null;
        }
        
        /**
         * 🚀 创建远程被控服务停止消息
         * 用于7777端口服务器停止时通知遥控端
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteControlServiceStopped(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String reason) {
            return null;
        }
        
        /**
         * 创建心跳消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createHeartbeat(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId) {
            return null;
        }
        
        /**
         * 创建H.264配置数据消息
         * 🚀 增强版：包含分辨率信息，提供双重验证机制
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createH264Config(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.Nullable()
        byte[] spsData, @org.jetbrains.annotations.Nullable()
        byte[] ppsData) {
            return null;
        }
        
        /**
         * 🚀 新增：创建包含分辨率信息的H.264配置数据消息
         * 提供WebSocket分辨率传递机制，作为SPS解析的补充验证
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createH264ConfigWithResolution(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.Nullable()
        byte[] spsData, @org.jetbrains.annotations.Nullable()
        byte[] ppsData, int width, int height) {
            return null;
        }
        
        /**
         * 🎯 横竖屏适配：创建包含分辨率和方向信息的H.264配置数据消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createH264ConfigWithResolutionAndOrientation(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.Nullable()
        byte[] spsData, @org.jetbrains.annotations.Nullable()
        byte[] ppsData, int width, int height, @org.jetbrains.annotations.Nullable()
        java.lang.Integer orientation, @org.jetbrains.annotations.Nullable()
        java.util.Map<java.lang.String, java.lang.Object> existingData) {
            return null;
        }
        
        /**
         * 创建AAC配置数据消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createAacConfig(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.Nullable()
        byte[] configData) {
            return null;
        }
        
        /**
         * 创建SSRC映射消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createSsrcMapping(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, long ssrc) {
            return null;
        }
        
        /**
         * 创建投屏状态同步消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createCastingState(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isCasting, @org.jetbrains.annotations.NotNull()
        java.lang.String reason) {
            return null;
        }
        
        /**
         * 创建码率控制消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createBitrateControl(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int bitrate, @org.jetbrains.annotations.Nullable()
        java.lang.String targetConnectionId) {
            return null;
        }
        
        /**
         * 创建媒体音频控制消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createMediaAudioControl(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isEnabled) {
            return null;
        }
        
        /**
         * 创建麦克风音频控制消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createMicAudioControl(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean isEnabled) {
            return null;
        }
        
        /**
         * 创建视频流停止消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createVideoStreamStop(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId) {
            return null;
        }
        
        /**
         * 创建功能控制消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createFunctionControl(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String functionType, boolean enabled) {
            return null;
        }
        
        /**
         * 创建远程控制请求消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteControlRequest(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String deviceName) {
            return null;
        }
        
        /**
         * 创建远程控制响应消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteControlResponse(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, boolean success, @org.jetbrains.annotations.NotNull()
        java.lang.String message) {
            return null;
        }
        
        /**
         * 创建远程码率变更消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteBitrateChange(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int bitrateMbps) {
            return null;
        }
        
        /**
         * 创建远程分辨率变更消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteResolutionChange(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int scalePercent) {
            return null;
        }
        
        /**
         * 创建远程音量变更消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteVolumeChange(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String volumeType, int volume) {
            return null;
        }
        
        /**
         * 创建远程连接切换消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteConnectionToggle(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String functionType, boolean enabled, @org.jetbrains.annotations.Nullable()
        java.lang.String targetConnectionId) {
            return null;
        }
        
        /**
         * 创建远程设置同步消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteSettingsSync(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.util.Map<java.lang.String, ? extends java.lang.Object> settings) {
            return null;
        }
        
        /**
         * 创建远程连接添加消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteConnectionAdded(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.util.Map<java.lang.String, ? extends java.lang.Object> connectionData) {
            return null;
        }
        
        /**
         * 创建远程连接更新消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteConnectionUpdated(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.util.Map<java.lang.String, ? extends java.lang.Object> connectionData) {
            return null;
        }
        
        /**
         * 创建远程连接删除消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteConnectionRemoved(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String removedConnectionId) {
            return null;
        }
        
        /**
         * 创建远程连接添加请求
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteConnectionAddRequest(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String ipAddress, int port) {
            return null;
        }
        
        /**
         * 创建远程连接编辑请求
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteConnectionEditRequest(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String targetConnectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String newIpAddress, int newPort) {
            return null;
        }
        
        /**
         * 创建远程连接删除请求
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createRemoteConnectionDeleteRequest(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String targetConnectionId) {
            return null;
        }
        
        /**
         * 创建截图请求消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createScreenshotRequest(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId) {
            return null;
        }
        
        /**
         * 创建截图响应消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createScreenshotResponse(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> screenshotDataList) {
            return null;
        }
        
        /**
         * 创建截图错误消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createScreenshotError(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String errorMessage) {
            return null;
        }
        
        /**
         * 创建文字内容请求消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createTextContentRequest(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId) {
            return null;
        }
        
        /**
         * 创建文字内容响应消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createTextContentResponse(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> textContentDataList) {
            return null;
        }
        
        /**
         * 创建文字内容错误消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createTextContentError(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.lang.String errorMessage) {
            return null;
        }
        
        /**
         * 创建文字格式同步消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createTextFormatSyncMessage(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
        java.util.Map<java.lang.String, ? extends java.lang.Object> formatData) {
            return null;
        }
        
        /**
         * 创建屏幕分辨率请求消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createScreenResolutionRequest(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId) {
            return null;
        }
        
        /**
         * 创建屏幕分辨率响应消息
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.websocket.ControlMessage createScreenResolutionResponse(@org.jetbrains.annotations.NotNull()
        java.lang.String connectionId, int width, int height) {
            return null;
        }
    }
}