package com.example.castapp.ui.view;

/**
 * 🪟 投屏窗口容器可视化View
 * 在远程接收端控制窗口中绘制投屏窗口容器的可视化框架
 * 🎯 重构：使用FrameLayout + 子View架构，每个窗口使用独立的View.clipBounds裁剪
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00a4\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010%\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u001b\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0010\u0000\n\u0002\b\r\u0018\u00002\u00020\u0001:\u0006~\u007f\u0080\u0001\u0081\u0001B%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\"\u0010@\u001a\u00020A2\u0018\u0010B\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020?\u0012\u0004\u0012\u00020\u00130C0<H\u0002J\u0012\u0010D\u001a\u0004\u0018\u00010:2\u0006\u0010E\u001a\u000209H\u0002J\u0006\u0010F\u001a\u00020AJ\u0010\u0010G\u001a\u00020A2\u0006\u0010H\u001a\u00020\u0013H\u0002J\u0010\u0010I\u001a\u00020\n2\u0006\u0010J\u001a\u00020\nH\u0002J\u0010\u0010K\u001a\u00020A2\u0006\u0010H\u001a\u00020\u0013H\u0002J\u000e\u0010L\u001a\u00020\u001c2\u0006\u0010M\u001a\u000209J\u001a\u0010N\u001a\u00020A2\u0006\u0010O\u001a\u00020\u001c2\b\u0010P\u001a\u0004\u0018\u00010)H\u0002J\u0010\u0010Q\u001a\u00020\u001c2\b\b\u0002\u0010O\u001a\u00020\u001cJ\u001a\u0010R\u001a\u0004\u0018\u00010?2\u0006\u0010S\u001a\u00020\n2\u0006\u0010T\u001a\u00020\nH\u0002J\f\u0010U\u001a\b\u0012\u0004\u0012\u00020\u00130<J\u000e\u0010V\u001a\u00020\n2\u0006\u0010M\u001a\u000209J\u0018\u0010W\u001a\u00020\u001c2\u0006\u0010S\u001a\u00020\n2\u0006\u0010T\u001a\u00020\nH\u0002J\u0018\u0010X\u001a\u00020\u001c2\u0006\u0010S\u001a\u00020\n2\u0006\u0010T\u001a\u00020\nH\u0002J\u0018\u0010Y\u001a\u00020\u001c2\u0006\u0010S\u001a\u00020\n2\u0006\u0010T\u001a\u00020\nH\u0002J\u0010\u0010Z\u001a\u00020\u001c2\u0006\u0010M\u001a\u000209H\u0002J\u000e\u0010[\u001a\u00020\u001c2\u0006\u0010M\u001a\u000209J\u0010\u0010\\\u001a\u00020A2\u0006\u0010M\u001a\u000209H\u0002J\u0010\u0010]\u001a\u00020\u001c2\u0006\u0010^\u001a\u00020_H\u0016J\b\u0010`\u001a\u00020AH\u0002J\b\u0010a\u001a\u00020AH\u0002J\u0010\u0010b\u001a\u00020A2\u0006\u0010M\u001a\u000209H\u0002J\u0010\u0010c\u001a\u00020A2\b\u0010d\u001a\u0004\u0018\u00010\'J\u0010\u0010e\u001a\u00020A2\u0006\u0010M\u001a\u000209H\u0002J\b\u0010f\u001a\u00020AH\u0002J\u0010\u0010g\u001a\u00020A2\u0006\u0010h\u001a\u00020?H\u0002J\u001a\u0010i\u001a\u00020A2\u0006\u0010j\u001a\u00020\u00132\b\u0010k\u001a\u0004\u0018\u00010)H\u0002J \u0010l\u001a\u00020A2\u0006\u0010j\u001a\u00020\u00132\u0006\u0010m\u001a\u00020\n2\u0006\u0010n\u001a\u00020\nH\u0002J\u0018\u0010o\u001a\u00020A2\u0006\u0010j\u001a\u00020\u00132\u0006\u0010p\u001a\u00020\nH\u0002J\u0018\u0010q\u001a\u00020A2\u0006\u0010j\u001a\u00020\u00132\u0006\u0010r\u001a\u00020\nH\u0002J \u0010s\u001a\u00020A2\u0018\u0010t\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u000209\u0012\u0004\u0012\u00020u080<J \u0010v\u001a\u00020A2\u0018\u0010w\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u000209\u0012\u0004\u0012\u00020u080<J\u0014\u0010x\u001a\u00020A2\f\u0010y\u001a\b\u0012\u0004\u0012\u00020\u00130<J\b\u0010z\u001a\u00020AH\u0002J(\u0010{\u001a\u00020A2\u001e\u0010|\u001a\u001a\u0012\u0004\u0012\u000209\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u000209\u0012\u0004\u0012\u00020u0808H\u0002J\b\u0010}\u001a\u00020AH\u0002R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0012\u001a\u0004\u0018\u00010\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\nX\u0082D\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001e\u0010 \u001a\u00020\u001c2\u0006\u0010\u001f\u001a\u00020\u001c@BX\u0086\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u000e\u0010\"\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010%\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010&\u001a\u0004\u0018\u00010\'X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010(\u001a\u0004\u0018\u00010)X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010+\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010,\u001a\u0004\u0018\u00010\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020.X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010/\u001a\u00020.X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00100\u001a\u000201X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00102\u001a\u00020.X\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u00103\u001a\u00020.X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00104\u001a\u000205X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u00106\u001a\u0004\u0018\u00010\u0013X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u00107\u001a\u000e\u0012\u0004\u0012\u000209\u0012\u0004\u0012\u00020:08X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00130<X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001a\u0010=\u001a\u000e\u0012\u0004\u0012\u000209\u0012\u0004\u0012\u00020?0>X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0082\u0001"}, d2 = {"Lcom/example/castapp/ui/view/WindowContainerVisualizationView;", "Landroid/widget/FrameLayout;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "baseAbsoluteRotationAngle", "", "baseAbsoluteScaleFactor", "buttonAbsoluteX", "buttonAbsoluteY", "cropControlButtons", "Landroid/widget/LinearLayout;", "cropOverlay", "Lcom/example/castapp/ui/view/CropVisualizationOverlay;", "croppedWindowData", "Lcom/example/castapp/model/WindowVisualizationData;", "currentRotationAngle", "currentScaleFactor", "dragOffsetX", "dragOffsetY", "dragThreshold", "draggedWindowData", "initialScaleFactor", "isCropping", "", "isDragging", "isRotating", "<set-?>", "isScaling", "()Z", "isScreenshotMode", "isVisualizationEnabled", "lastTouchX", "lastTouchY", "onWindowDragListener", "Lcom/example/castapp/ui/view/WindowContainerVisualizationView$OnWindowDragListener;", "originalCropRatioForCancel", "Landroid/graphics/RectF;", "originalWindowHeight", "originalWindowWidth", "rotatedWindowData", "rotationEndDelay", "", "rotationEndTime", "rotationGestureDetector", "Lcom/example/castapp/ui/view/WindowContainerVisualizationView$RotationGestureDetector;", "scaleEndDelay", "scaleEndTime", "scaleGestureDetector", "Landroid/view/ScaleGestureDetector;", "scaledWindowData", "screenshotDataMap", "", "", "Landroid/graphics/Bitmap;", "visualizationDataList", "", "windowContainerViews", "", "Lcom/example/castapp/ui/view/WindowVisualizationContainerView;", "adjustExistingViewsLayerOrder", "", "existingViews", "Lkotlin/Pair;", "base64ToBitmap", "base64String", "clearVisualization", "createCropControlButtonsInParent", "windowData", "dpToPx", "dp", "enterCropMode", "enterCropModeByConnectionId", "connectionId", "exitCropMode", "isCancel", "finalCropRatio", "exitCurrentCropMode", "findClickedWindowView", "x", "y", "getVisualizationDataList", "getWindowActualScaleFactor", "handleTouchDownWithSubViews", "handleTouchMove", "handleTouchUp", "isTextWindow", "isWindowInCropMode", "loadButtonPosition", "onTouchEvent", "event", "Landroid/view/MotionEvent;", "removeCropControlButtons", "resetScaleState", "saveButtonPosition", "setOnWindowDragListener", "listener", "setupCropControlButtonsDrag", "setupCropControlButtonsEvents", "syncBorderTransformWithContainer", "containerView", "updateCroppedWindowVisualization", "originalData", "cropRatio", "updateDraggedWindowPosition", "newX", "newY", "updateRotatedWindowVisualization", "rotationDelta", "updateScaledWindowVisualization", "scaleFactor", "updateScreenshots", "screenshotsData", "", "updateTextContents", "textContentsData", "updateVisualizationData", "dataList", "updateVisualizationDataWithScreenshots", "updateVisualizationDataWithTextFormats", "textFormatMap", "updateWindowContainerViews", "OnWindowDragListener", "RotationGestureDetector", "RotationGestureListener", "ScaleGestureListener", "app_debug"})
public final class WindowContainerVisualizationView extends android.widget.FrameLayout {
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.example.castapp.model.WindowVisualizationData> visualizationDataList;
    private boolean isVisualizationEnabled = false;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, com.example.castapp.ui.view.WindowVisualizationContainerView> windowContainerViews = null;
    @org.jetbrains.annotations.Nullable()
    private android.widget.LinearLayout cropControlButtons;
    private float buttonAbsoluteX = -1.0F;
    private float buttonAbsoluteY = -1.0F;
    @org.jetbrains.annotations.Nullable()
    private android.graphics.RectF originalCropRatioForCancel;
    @org.jetbrains.annotations.NotNull()
    private java.util.Map<java.lang.String, android.graphics.Bitmap> screenshotDataMap;
    private boolean isScreenshotMode = false;
    private boolean isDragging = false;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.model.WindowVisualizationData draggedWindowData;
    private float lastTouchX = 0.0F;
    private float lastTouchY = 0.0F;
    private float dragOffsetX = 0.0F;
    private float dragOffsetY = 0.0F;
    private final float dragThreshold = 15.0F;
    private boolean isScaling = false;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.model.WindowVisualizationData scaledWindowData;
    private float initialScaleFactor = 1.0F;
    private float currentScaleFactor = 1.0F;
    private float originalWindowWidth = 0.0F;
    private float originalWindowHeight = 0.0F;
    private float baseAbsoluteScaleFactor = 1.0F;
    private long scaleEndTime = 0L;
    private final long scaleEndDelay = 50L;
    @org.jetbrains.annotations.NotNull()
    private android.view.ScaleGestureDetector scaleGestureDetector;
    private boolean isRotating = false;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.model.WindowVisualizationData rotatedWindowData;
    private float currentRotationAngle = 0.0F;
    private float baseAbsoluteRotationAngle = 0.0F;
    private long rotationEndTime = 0L;
    private final long rotationEndDelay = 100L;
    @org.jetbrains.annotations.NotNull()
    private com.example.castapp.ui.view.WindowContainerVisualizationView.RotationGestureDetector rotationGestureDetector;
    private boolean isCropping = false;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.model.WindowVisualizationData croppedWindowData;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.CropVisualizationOverlay cropOverlay;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.WindowContainerVisualizationView.OnWindowDragListener onWindowDragListener;
    
    @kotlin.jvm.JvmOverloads()
    public WindowContainerVisualizationView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    public final boolean isScaling() {
        return false;
    }
    
    /**
     * 更新可视化数据
     * @param dataList 窗口可视化数据列表
     * 🎯 重构：管理子View而不是重绘Canvas
     */
    public final void updateVisualizationData(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.castapp.model.WindowVisualizationData> dataList) {
    }
    
    /**
     * 🎯 层级修复：管理窗口容器子View（优化层级排序逻辑）
     */
    private final void updateWindowContainerViews() {
    }
    
    /**
     * 🎯 层级修复：调整现有View的层级顺序
     */
    private final void adjustExistingViewsLayerOrder(java.util.List<kotlin.Pair<com.example.castapp.ui.view.WindowVisualizationContainerView, com.example.castapp.model.WindowVisualizationData>> existingViews) {
    }
    
    /**
     * 清除可视化数据
     * 🎯 重构：清除所有子View
     */
    public final void clearVisualization() {
    }
    
    /**
     * 🎯 设置窗口拖动监听器
     */
    public final void setOnWindowDragListener(@org.jetbrains.annotations.Nullable()
    com.example.castapp.ui.view.WindowContainerVisualizationView.OnWindowDragListener listener) {
    }
    
    /**
     * 📸 更新截图数据
     * 🎯 重构：更新子View中的截图数据
     */
    public final void updateScreenshots(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> screenshotsData) {
    }
    
    /**
     * 📝 更新文字内容数据
     * 🎯 重构：更新子View中的文字内容数据（包含富文本格式）
     */
    public final void updateTextContents(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> textContentsData) {
    }
    
    /**
     * 🎯 新增：更新可视化数据中的截图信息
     */
    private final void updateVisualizationDataWithScreenshots() {
    }
    
    /**
     * 🎯 更新可视化数据中的文字格式信息（包含富文本格式）
     */
    private final void updateVisualizationDataWithTextFormats(java.util.Map<java.lang.String, ? extends java.util.Map<java.lang.String, ? extends java.lang.Object>> textFormatMap) {
    }
    
    /**
     * 📸 将Base64字符串转换为Bitmap
     * 🎯 优化：使用高质量解码选项
     */
    private final android.graphics.Bitmap base64ToBitmap(java.lang.String base64String) {
        return null;
    }
    
    /**
     * 🎯 dp转px工具方法（与接收端TransformHandler保持一致）
     */
    private final float dpToPx(float dp) {
        return 0.0F;
    }
    
    @java.lang.Override()
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull()
    android.view.MotionEvent event) {
        return false;
    }
    
    /**
     * 🎯 新增：基于子View架构的触摸按下事件处理
     */
    private final boolean handleTouchDownWithSubViews(float x, float y) {
        return false;
    }
    
    /**
     * 🎯 新增：查找被点击的窗口子View
     */
    private final com.example.castapp.ui.view.WindowVisualizationContainerView findClickedWindowView(float x, float y) {
        return null;
    }
    
    /**
     * 处理触摸移动事件
     */
    private final boolean handleTouchMove(float x, float y) {
        return false;
    }
    
    /**
     * 处理触摸抬起事件
     */
    private final boolean handleTouchUp(float x, float y) {
        return false;
    }
    
    /**
     * 判断是否为文字窗口
     */
    private final boolean isTextWindow(java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 更新被拖动窗口的位置
     * 🎯 关键修复：使用实时同步机制，避免拖动过程中边框消失
     */
    private final void updateDraggedWindowPosition(com.example.castapp.model.WindowVisualizationData originalData, float newX, float newY) {
    }
    
    /**
     * 更新被缩放窗口的可视化效果
     */
    private final void updateScaledWindowVisualization(com.example.castapp.model.WindowVisualizationData originalData, float scaleFactor) {
    }
    
    /**
     * 🎯 新增：重置缩放状态的统一方法
     */
    private final void resetScaleState() {
    }
    
    /**
     * 更新被旋转窗口的可视化效果
     */
    private final void updateRotatedWindowVisualization(com.example.castapp.model.WindowVisualizationData originalData, float rotationDelta) {
    }
    
    /**
     * 🎯 新增：公共方法 - 根据连接ID进入裁剪模式
     * @param connectionId 窗口连接ID
     * @return 是否成功进入裁剪模式
     */
    public final boolean enterCropModeByConnectionId(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 🎯 新增：公共方法 - 退出当前裁剪模式
     * @param isCancel 是否为取消操作
     * @return 是否成功退出裁剪模式
     */
    public final boolean exitCurrentCropMode(boolean isCancel) {
        return false;
    }
    
    /**
     * 进入裁剪模式
     * 🎯 重构：使用窗口容器内部的裁剪覆盖层，与接收端保持一致
     */
    private final void enterCropMode(com.example.castapp.model.WindowVisualizationData windowData) {
    }
    
    /**
     * 退出裁剪模式
     */
    private final void exitCropMode(boolean isCancel, android.graphics.RectF finalCropRatio) {
    }
    
    /**
     * 🎯 新增：检查指定窗口是否处于裁剪模式
     */
    public final boolean isWindowInCropMode(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 🎯 新增：在父容器中创建裁剪控制按钮组（与接收端CropManager保持一致）
     */
    private final void createCropControlButtonsInParent(com.example.castapp.model.WindowVisualizationData windowData) {
    }
    
    /**
     * 🎯 新增：设置裁剪控制按钮事件
     */
    private final void setupCropControlButtonsEvents() {
    }
    
    /**
     * 🎯 新增：设置裁剪控制按钮拖动功能（完全参考接收端CropManager）
     */
    private final void setupCropControlButtonsDrag(java.lang.String connectionId) {
    }
    
    /**
     * 🎯 新增：保存按钮组位置到SharedPreferences（参考接收端CropManager）
     */
    private final void saveButtonPosition(java.lang.String connectionId) {
    }
    
    /**
     * 🎯 新增：从SharedPreferences加载按钮组位置（参考接收端CropManager）
     */
    private final void loadButtonPosition(java.lang.String connectionId) {
    }
    
    /**
     * 🎯 新增：移除裁剪控制按钮
     */
    private final void removeCropControlButtons() {
    }
    
    /**
     * 🎯 更新被裁剪窗口的可视化效果
     */
    private final void updateCroppedWindowVisualization(com.example.castapp.model.WindowVisualizationData originalData, android.graphics.RectF cropRatio) {
    }
    
    /**
     * 🎯 新增：获取当前可视化数据列表
     * 用于外部更新可视化数据
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.castapp.model.WindowVisualizationData> getVisualizationDataList() {
        return null;
    }
    
    /**
     * 🎯 新增：获取特定窗口的实际缩放值
     * 用于批量同步时获取用户实际缩放后的参数
     */
    public final float getWindowActualScaleFactor(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId) {
        return 0.0F;
    }
    
    /**
     * 🎯 关键修复：同步边框View与容器View的变换属性
     * 确保边框在缩放、旋转、移动时与窗口容器保持完全同步
     */
    private final void syncBorderTransformWithContainer(com.example.castapp.ui.view.WindowVisualizationContainerView containerView) {
    }
    
    @kotlin.jvm.JvmOverloads()
    public WindowContainerVisualizationView(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public WindowContainerVisualizationView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0010\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&J\"\u0010\b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\u0010\t\u001a\u0004\u0018\u00010\u00072\u0006\u0010\n\u001a\u00020\u000bH&J\u0010\u0010\f\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J \u0010\r\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000fH&J \u0010\u0011\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0012\u001a\u00020\u000f2\u0006\u0010\u0013\u001a\u00020\u000fH&J\u0010\u0010\u0014\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J(\u0010\u0015\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000fH&J\u0018\u0010\u0017\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0018\u001a\u00020\u000fH&J\u0010\u0010\u0019\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J(\u0010\u001a\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u001b\u001a\u00020\u000f2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000fH&J\u0018\u0010\u001c\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u001d\u001a\u00020\u000fH&J\u0010\u0010\u001e\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\u001f"}, d2 = {"Lcom/example/castapp/ui/view/WindowContainerVisualizationView$OnWindowDragListener;", "", "onCropAreaChange", "", "windowData", "Lcom/example/castapp/model/WindowVisualizationData;", "cropRatio", "Landroid/graphics/RectF;", "onCropModeEnd", "finalCropRatio", "isCancel", "", "onCropModeStart", "onDragEnd", "finalX", "", "finalY", "onDragMove", "newX", "newY", "onDragStart", "onRotationEnd", "finalRotationAngle", "onRotationMove", "rotationAngle", "onRotationStart", "onScaleEnd", "finalScaleFactor", "onScaleMove", "scaleFactor", "onScaleStart", "app_debug"})
    public static abstract interface OnWindowDragListener {
        
        /**
         * 拖动开始
         * @param windowData 被拖动的窗口数据
         */
        public abstract void onDragStart(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.WindowVisualizationData windowData);
        
        /**
         * 拖动中
         * @param windowData 被拖动的窗口数据
         * @param newX 新的X坐标（远程控制窗口坐标系）
         * @param newY 新的Y坐标（远程控制窗口坐标系）
         */
        public abstract void onDragMove(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.WindowVisualizationData windowData, float newX, float newY);
        
        /**
         * 拖动结束
         * @param windowData 被拖动的窗口数据
         * @param finalX 最终X坐标（远程控制窗口坐标系）
         * @param finalY 最终Y坐标（远程控制窗口坐标系）
         */
        public abstract void onDragEnd(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.WindowVisualizationData windowData, float finalX, float finalY);
        
        /**
         * 缩放开始
         * @param windowData 被缩放的窗口数据
         */
        public abstract void onScaleStart(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.WindowVisualizationData windowData);
        
        /**
         * 缩放中
         * @param windowData 被缩放的窗口数据
         * @param scaleFactor 当前缩放因子
         */
        public abstract void onScaleMove(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.WindowVisualizationData windowData, float scaleFactor);
        
        /**
         * 缩放结束
         * @param windowData 被缩放的窗口数据
         * @param finalScaleFactor 最终缩放因子（绝对值）
         * @param finalX 缩放后的最终X坐标（远程控制窗口坐标系）
         * @param finalY 缩放后的最终Y坐标（远程控制窗口坐标系）
         */
        public abstract void onScaleEnd(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.WindowVisualizationData windowData, float finalScaleFactor, float finalX, float finalY);
        
        /**
         * 旋转开始
         * @param windowData 被旋转的窗口数据
         */
        public abstract void onRotationStart(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.WindowVisualizationData windowData);
        
        /**
         * 旋转中
         * @param windowData 被旋转的窗口数据
         * @param rotationAngle 当前旋转角度
         */
        public abstract void onRotationMove(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.WindowVisualizationData windowData, float rotationAngle);
        
        /**
         * 旋转结束
         * @param windowData 被旋转的窗口数据
         * @param finalRotationAngle 最终旋转角度（绝对值）
         * @param finalX 旋转后的最终X坐标（远程控制窗口坐标系）
         * @param finalY 旋转后的最终Y坐标（远程控制窗口坐标系）
         */
        public abstract void onRotationEnd(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.WindowVisualizationData windowData, float finalRotationAngle, float finalX, float finalY);
        
        /**
         * 裁剪模式开始
         * @param windowData 进入裁剪模式的窗口数据
         */
        public abstract void onCropModeStart(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.WindowVisualizationData windowData);
        
        /**
         * 裁剪区域变化
         * @param windowData 被裁剪的窗口数据
         * @param cropRatio 当前裁剪区域比例
         */
        public abstract void onCropAreaChange(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.WindowVisualizationData windowData, @org.jetbrains.annotations.NotNull()
        android.graphics.RectF cropRatio);
        
        /**
         * 裁剪模式结束
         * @param windowData 被裁剪的窗口数据
         * @param finalCropRatio 最终裁剪区域比例，null表示取消裁剪
         * @param isCancel 是否为取消操作
         */
        public abstract void onCropModeEnd(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.WindowVisualizationData windowData, @org.jetbrains.annotations.Nullable()
        android.graphics.RectF finalCropRatio, boolean isCancel);
    }
    
    /**
     * 自定义旋转手势检测器（复用TransformHandler的实现）
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0002\u0018\u00002\u00020\u0001:\u0001\u0013B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\u000e\u0010\u0012\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u0011R\u001e\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0006@BX\u0086\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u001e\u0010\n\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0006@BX\u0086\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\tR\u000e\u0010\f\u001a\u00020\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/example/castapp/ui/view/WindowContainerVisualizationView$RotationGestureDetector;", "", "listener", "Lcom/example/castapp/ui/view/WindowContainerVisualizationView$RotationGestureDetector$OnRotationGestureListener;", "(Lcom/example/castapp/ui/view/WindowContainerVisualizationView$RotationGestureDetector$OnRotationGestureListener;)V", "<set-?>", "", "focusX", "getFocusX", "()F", "focusY", "getFocusY", "isInProgress", "", "prevAngle", "getAngle", "event", "Landroid/view/MotionEvent;", "onTouchEvent", "OnRotationGestureListener", "app_debug"})
    static final class RotationGestureDetector {
        @org.jetbrains.annotations.NotNull()
        private final com.example.castapp.ui.view.WindowContainerVisualizationView.RotationGestureDetector.OnRotationGestureListener listener = null;
        private float prevAngle = 0.0F;
        private boolean isInProgress = false;
        private float focusX = 0.0F;
        private float focusY = 0.0F;
        
        public RotationGestureDetector(@org.jetbrains.annotations.NotNull()
        com.example.castapp.ui.view.WindowContainerVisualizationView.RotationGestureDetector.OnRotationGestureListener listener) {
            super();
        }
        
        public final float getFocusX() {
            return 0.0F;
        }
        
        public final float getFocusY() {
            return 0.0F;
        }
        
        public final boolean onTouchEvent(@org.jetbrains.annotations.NotNull()
        android.view.MotionEvent event) {
            return false;
        }
        
        private final float getAngle(android.view.MotionEvent event) {
            return 0.0F;
        }
        
        @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\u0018\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H&J\u0010\u0010\b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H&J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u0004\u001a\u00020\u0005H&\u00a8\u0006\u000b"}, d2 = {"Lcom/example/castapp/ui/view/WindowContainerVisualizationView$RotationGestureDetector$OnRotationGestureListener;", "", "onRotate", "", "detector", "Lcom/example/castapp/ui/view/WindowContainerVisualizationView$RotationGestureDetector;", "rotationDelta", "", "onRotationBegin", "onRotationEnd", "", "app_debug"})
        public static abstract interface OnRotationGestureListener {
            
            public abstract boolean onRotationBegin(@org.jetbrains.annotations.NotNull()
            com.example.castapp.ui.view.WindowContainerVisualizationView.RotationGestureDetector detector);
            
            public abstract boolean onRotate(@org.jetbrains.annotations.NotNull()
            com.example.castapp.ui.view.WindowContainerVisualizationView.RotationGestureDetector detector, float rotationDelta);
            
            public abstract void onRotationEnd(@org.jetbrains.annotations.NotNull()
            com.example.castapp.ui.view.WindowContainerVisualizationView.RotationGestureDetector detector);
        }
    }
    
    /**
     * 旋转手势监听器
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0016J\u0010\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u0010\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0005\u001a\u00020\u0006H\u0016\u00a8\u0006\f"}, d2 = {"Lcom/example/castapp/ui/view/WindowContainerVisualizationView$RotationGestureListener;", "Lcom/example/castapp/ui/view/WindowContainerVisualizationView$RotationGestureDetector$OnRotationGestureListener;", "(Lcom/example/castapp/ui/view/WindowContainerVisualizationView;)V", "onRotate", "", "detector", "Lcom/example/castapp/ui/view/WindowContainerVisualizationView$RotationGestureDetector;", "rotationDelta", "", "onRotationBegin", "onRotationEnd", "", "app_debug"})
    final class RotationGestureListener implements com.example.castapp.ui.view.WindowContainerVisualizationView.RotationGestureDetector.OnRotationGestureListener {
        
        public RotationGestureListener() {
            super();
        }
        
        @java.lang.Override()
        public boolean onRotationBegin(@org.jetbrains.annotations.NotNull()
        com.example.castapp.ui.view.WindowContainerVisualizationView.RotationGestureDetector detector) {
            return false;
        }
        
        @java.lang.Override()
        public boolean onRotate(@org.jetbrains.annotations.NotNull()
        com.example.castapp.ui.view.WindowContainerVisualizationView.RotationGestureDetector detector, float rotationDelta) {
            return false;
        }
        
        @java.lang.Override()
        public void onRotationEnd(@org.jetbrains.annotations.NotNull()
        com.example.castapp.ui.view.WindowContainerVisualizationView.RotationGestureDetector detector) {
        }
    }
    
    /**
     * 缩放手势监听器
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u0010\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u0010\u0010\b\u001a\u00020\t2\u0006\u0010\u0005\u001a\u00020\u0006H\u0016\u00a8\u0006\n"}, d2 = {"Lcom/example/castapp/ui/view/WindowContainerVisualizationView$ScaleGestureListener;", "Landroid/view/ScaleGestureDetector$OnScaleGestureListener;", "(Lcom/example/castapp/ui/view/WindowContainerVisualizationView;)V", "onScale", "", "detector", "Landroid/view/ScaleGestureDetector;", "onScaleBegin", "onScaleEnd", "", "app_debug"})
    final class ScaleGestureListener implements android.view.ScaleGestureDetector.OnScaleGestureListener {
        
        public ScaleGestureListener() {
            super();
        }
        
        @java.lang.Override()
        public boolean onScaleBegin(@org.jetbrains.annotations.NotNull()
        android.view.ScaleGestureDetector detector) {
            return false;
        }
        
        @java.lang.Override()
        public boolean onScale(@org.jetbrains.annotations.NotNull()
        android.view.ScaleGestureDetector detector) {
            return false;
        }
        
        @java.lang.Override()
        public void onScaleEnd(@org.jetbrains.annotations.NotNull()
        android.view.ScaleGestureDetector detector) {
        }
    }
}