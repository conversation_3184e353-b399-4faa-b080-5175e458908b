package com.example.castapp.ui.view;

/**
 * 🪟 单个窗口容器可视化View
 * 使用View.clipBounds实现裁剪，与接收端CropManager保持一致
 * 🎯 修复：改为FrameLayout以支持裁剪覆盖层
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0080\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0016\u0018\u00002\u00020\u0001B%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0010\u0010!\u001a\u00020\u00112\u0006\u0010\"\u001a\u00020 H\u0002J\u0010\u0010#\u001a\u00020\u00112\u0006\u0010$\u001a\u00020\u0010H\u0002J\b\u0010%\u001a\u00020\u0011H\u0002J\b\u0010&\u001a\u00020\u0011H\u0016J\b\u0010\'\u001a\u00020\u0011H\u0002J\u0018\u0010(\u001a\u00020\u00112\u0006\u0010\"\u001a\u00020 2\u0006\u0010)\u001a\u00020*H\u0002J\u0010\u0010+\u001a\u00020\u00112\u0006\u0010\"\u001a\u00020 H\u0002J\u0010\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020-H\u0002J \u0010/\u001a\u00020\u00112\u0006\u00100\u001a\u0002012\u0006\u00102\u001a\u00020\u00102\u0006\u0010\"\u001a\u00020 H\u0002J(\u00103\u001a\u00020\u00112\u0006\u00100\u001a\u0002012\u0006\u00104\u001a\u0002052\u0006\u00102\u001a\u00020\u00102\u0006\u0010\"\u001a\u00020 H\u0002J\u0018\u00106\u001a\u00020\u00112\u0006\u00100\u001a\u0002012\u0006\u0010\"\u001a\u00020 H\u0002J\u000e\u00107\u001a\u00020\u00112\u0006\u00108\u001a\u00020\nJ\b\u00109\u001a\u0004\u0018\u00010 J\u0010\u0010:\u001a\u00020\n2\u0006\u0010;\u001a\u00020\u0018H\u0002J\u0010\u0010<\u001a\u00020\n2\u0006\u0010;\u001a\u00020\u0018H\u0002J\b\u0010=\u001a\u00020\u0011H\u0014J\b\u0010>\u001a\u00020\u0011H\u0014J\u0010\u0010?\u001a\u00020\u00112\u0006\u00100\u001a\u000201H\u0014J\b\u0010@\u001a\u00020\u0011H\u0002J\b\u0010A\u001a\u00020\u0011H\u0002J\u000e\u0010B\u001a\u00020\u00112\u0006\u0010\"\u001a\u00020 J\u001a\u0010C\u001a\u00020\u00112\u0006\u0010\"\u001a\u00020 2\b\b\u0002\u0010D\u001a\u00020\nH\u0002J\u0010\u0010E\u001a\u00020\u00112\u0006\u0010\"\u001a\u00020 H\u0002J,\u0010F\u001a\u00020\u00112\b\u0010G\u001a\u0004\u0018\u00010\u00102\u001a\u0010H\u001a\u0016\u0012\u0006\u0012\u0004\u0018\u00010\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00110\u000fJ\u0010\u0010I\u001a\u00020\u00112\u0006\u0010\"\u001a\u00020 H\u0002J\u0010\u0010J\u001a\u00020\u00112\u0006\u0010\"\u001a\u00020 H\u0002R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\r\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R$\u0010\u000e\u001a\u0018\u0012\u0006\u0012\u0004\u0018\u00010\u0010\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u0011\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\"\u0010\u0014\u001a\u0004\u0018\u00010\u00132\b\u0010\u0012\u001a\u0004\u0018\u00010\u0013@BX\u0086\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001d\u001a\u0004\u0018\u00010\u001eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001f\u001a\u0004\u0018\u00010 X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006K"}, d2 = {"Lcom/example/castapp/ui/view/WindowVisualizationContainerView;", "Landroid/widget/FrameLayout;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "borderStateBeforeCrop", "", "borderViewRef", "Ljava/lang/ref/WeakReference;", "Landroid/view/View;", "cropModeCallback", "Lkotlin/Function2;", "Landroid/graphics/RectF;", "", "<set-?>", "Lcom/example/castapp/ui/view/CropOverlayView;", "cropOverlay", "getCropOverlay", "()Lcom/example/castapp/ui/view/CropOverlayView;", "currentTextContent", "", "isCropping", "isDetached", "textFormatParser", "Lcom/example/castapp/utils/RemoteTextFormatParser;", "textView", "Landroid/widget/TextView;", "windowData", "Lcom/example/castapp/model/WindowVisualizationData;", "applyClipBounds", "data", "applyFinalCropBounds", "cropRatio", "bringBorderToFront", "bringToFront", "cleanupExistingBorder", "createBorderView", "parentView", "Landroid/view/ViewGroup;", "createTextView", "dpToPx", "", "dp", "drawCameraPlaceholder", "canvas", "Landroid/graphics/Canvas;", "bounds", "drawScreenshot", "bitmap", "Landroid/graphics/Bitmap;", "drawWindowContent", "endCropMode", "isCancel", "getWindowData", "isCameraPlaceholder", "connectionId", "isTextWindow", "onAttachedToWindow", "onDetachedFromWindow", "onDraw", "removeBorderView", "safeRemoveBorderView", "setWindowData", "setWindowDataInternal", "forceRefresh", "setupTextView", "startCropMode", "originalCropRatio", "callback", "updateBorderView", "updateTextView", "app_debug"})
public final class WindowVisualizationContainerView extends android.widget.FrameLayout {
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.model.WindowVisualizationData windowData;
    @org.jetbrains.annotations.Nullable()
    private com.example.castapp.ui.view.CropOverlayView cropOverlay;
    private boolean isCropping = false;
    @org.jetbrains.annotations.Nullable()
    private kotlin.jvm.functions.Function2<? super android.graphics.RectF, ? super java.lang.Boolean, kotlin.Unit> cropModeCallback;
    @org.jetbrains.annotations.Nullable()
    private java.lang.ref.WeakReference<android.view.View> borderViewRef;
    private boolean isDetached = false;
    private boolean borderStateBeforeCrop = false;
    @org.jetbrains.annotations.Nullable()
    private android.widget.TextView textView;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String currentTextContent = "";
    @org.jetbrains.annotations.NotNull()
    private final com.example.castapp.utils.RemoteTextFormatParser textFormatParser = null;
    
    @kotlin.jvm.JvmOverloads()
    public WindowVisualizationContainerView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.ui.view.CropOverlayView getCropOverlay() {
        return null;
    }
    
    /**
     * 设置窗口数据并更新显示
     */
    public final void setWindowData(@org.jetbrains.annotations.NotNull()
    com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 🎯 获取窗口数据
     */
    @org.jetbrains.annotations.Nullable()
    public final com.example.castapp.model.WindowVisualizationData getWindowData() {
        return null;
    }
    
    /**
     * 内部窗口数据设置方法，支持强制刷新
     */
    private final void setWindowDataInternal(com.example.castapp.model.WindowVisualizationData data, boolean forceRefresh) {
    }
    
    /**
     * 🎯 应用View.clipBounds裁剪，与接收端CropManager保持一致
     */
    private final void applyClipBounds(com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 判断是否为文字窗口
     */
    private final boolean isTextWindow(java.lang.String connectionId) {
        return false;
    }
    
    /**
     * 判断是否为摄像头占位容器
     * 🎯 根本解决方案：识别摄像头占位容器的独特ID格式
     */
    private final boolean isCameraPlaceholder(java.lang.String connectionId) {
        return false;
    }
    
    @java.lang.Override()
    protected void onAttachedToWindow() {
    }
    
    @java.lang.Override()
    protected void onDetachedFromWindow() {
    }
    
    @java.lang.Override()
    protected void onDraw(@org.jetbrains.annotations.NotNull()
    android.graphics.Canvas canvas) {
    }
    
    /**
     * 绘制窗口内容
     */
    private final void drawWindowContent(android.graphics.Canvas canvas, com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 绘制截图（非文字窗口使用）
     */
    private final void drawScreenshot(android.graphics.Canvas canvas, android.graphics.Bitmap bitmap, android.graphics.RectF bounds, com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 🎯 新增：开始裁剪模式
     */
    public final void startCropMode(@org.jetbrains.annotations.Nullable()
    android.graphics.RectF originalCropRatio, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super android.graphics.RectF, ? super java.lang.Boolean, kotlin.Unit> callback) {
    }
    
    /**
     * 🎯 新增：结束裁剪模式
     */
    public final void endCropMode(boolean isCancel) {
    }
    
    /**
     * 🎯 新增：应用最终裁剪边界
     */
    private final void applyFinalCropBounds(android.graphics.RectF cropRatio) {
    }
    
    /**
     * 🎯 优雅边框管理：状态驱动的边框更新
     */
    private final void updateBorderView(com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 🎯 优雅边框管理：简洁的边框清理
     */
    private final void cleanupExistingBorder() {
    }
    
    /**
     * 🎯 优雅边框管理：创建独立边框View
     */
    private final void createBorderView(com.example.castapp.model.WindowVisualizationData data, android.view.ViewGroup parentView) {
    }
    
    /**
     * 🎯 修复：安全移除边框View
     */
    private final void safeRemoveBorderView() {
    }
    
    /**
     * 🎯 兼容：保留原有接口
     */
    private final void removeBorderView() {
    }
    
    /**
     * 🎯 层级修复：重写bringToFront方法，确保边框View同步调整层级
     */
    @java.lang.Override()
    public void bringToFront() {
    }
    
    /**
     * 🎯 层级修复：将边框View移到前台（层级管理时调用）
     */
    private final void bringBorderToFront() {
    }
    
    /**
     * dp转px工具方法
     */
    private final float dpToPx(float dp) {
        return 0.0F;
    }
    
    /**
     * 设置或更新TextView控件
     */
    private final void setupTextView(com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 创建TextView控件
     */
    private final void createTextView(com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 更新TextView控件（支持富文本格式）
     */
    private final void updateTextView(com.example.castapp.model.WindowVisualizationData data) {
    }
    
    /**
     * 绘制摄像头占位内容
     * 🎯 新增：为摄像头占位容器绘制图标和文字
     */
    private final void drawCameraPlaceholder(android.graphics.Canvas canvas, android.graphics.RectF bounds, com.example.castapp.model.WindowVisualizationData data) {
    }
    
    @kotlin.jvm.JvmOverloads()
    public WindowVisualizationContainerView(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads()
    public WindowVisualizationContainerView(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    android.util.AttributeSet attrs) {
        super(null);
    }
}