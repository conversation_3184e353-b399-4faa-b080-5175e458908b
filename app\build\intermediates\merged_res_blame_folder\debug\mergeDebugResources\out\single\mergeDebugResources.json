[{"merged": "com.example.castapp-debug-44:/drawable_ic_font.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_font.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_drag_handle.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_drag_handle.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_receive.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_receive.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_item_normal_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/item_normal_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_add_camera.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_add_camera.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_item_selected_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/item_selected_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_remote_control.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_remote_control.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_clear.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_clear.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_edit_layout.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_edit_layout.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_format_bold.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_format_bold.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_button_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/button_background.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_media_selection.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_media_selection.xml"}, {"merged": "com.example.castapp-debug-44:/layout_spinner_text_alignment_item.xml.flat", "source": "com.example.castapp-main-46:/layout/spinner_text_alignment_item.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_letter_spacing_settings.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_letter_spacing_settings.xml"}, {"merged": "com.example.castapp-debug-44:/layout_item_color_palette.xml.flat", "source": "com.example.castapp-main-46:/layout/item_color_palette.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_send.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_send.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_remote_receiver_settings_control.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_remote_receiver_settings_control.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_notification.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_notification.xml"}, {"merged": "com.example.castapp-debug-44:/layout_item_window_settings.xml.flat", "source": "com.example.castapp-main-46:/layout/item_window_settings.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_format_italic.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_format_italic.xml"}, {"merged": "com.example.castapp-debug-44:/layout_floating_stopwatch_window.xml.flat", "source": "com.example.castapp-main-46:/layout/floating_stopwatch_window.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_info_card_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/info_card_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_folder.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_folder.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_crop_button_reset_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/crop_button_reset_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_arrow_back.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_arrow_back.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_edittext_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/edittext_background.xml"}, {"merged": "com.example.castapp-debug-44:/layout_item_remote_connection_control.xml.flat", "source": "com.example.castapp-main-46:/layout/item_remote_connection_control.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_button_apply_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/button_apply_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_upload.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_upload.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_crop_button_cancel_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/crop_button_cancel_background.xml"}, {"merged": "com.example.castapp-debug-44:/layout_item_font_file.xml.flat", "source": "com.example.castapp-main-46:/layout/item_font_file.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_font_settings.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_font_settings.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_save_director_layout.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_save_director_layout.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_check.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_check.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_layer.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_layer.xml"}, {"merged": "com.example.castapp-debug-44:/layout_item_line_spacing.xml.flat", "source": "com.example.castapp-main-46:/layout/item_line_spacing.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_color_picker.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_color_picker.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_director.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_director.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_item_applied_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/item_applied_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_file.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_file.xml"}, {"merged": "com.example.castapp-debug-44:/layout_item_font_size_setting.xml.flat", "source": "com.example.castapp-main-46:/layout/item_font_size_setting.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_font_size_settings.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_font_size_settings.xml"}, {"merged": "com.example.castapp-debug-44:/layout_item_remote_connection.xml.flat", "source": "com.example.castapp-main-46:/layout/item_remote_connection.xml"}, {"merged": "com.example.castapp-debug-44:/layout_layout_text_edit_panel.xml.flat", "source": "com.example.castapp-main-46:/layout/layout_text_edit_panel.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_rounded_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/rounded_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_format_align_left.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_format_align_left.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_crop_button_apply_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/crop_button_apply_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_palette.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_palette.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_send.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_send.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_font_file_picker.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_font_file_picker.xml"}, {"merged": "com.example.castapp-debug-44:/layout_spinner_letter_spacing_dropdown_item.xml.flat", "source": "com.example.castapp-main-46:/layout/spinner_letter_spacing_dropdown_item.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_button_primary_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/button_primary_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_edit.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_edit.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_circle_green.xml.flat", "source": "com.example.castapp-main-46:/drawable/circle_green.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_remote_sender_control.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_remote_sender_control.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_wheel.xml.flat", "source": "com.example.castapp-main-46:/drawable/wheel.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_add_receiver.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_add_receiver.xml"}, {"merged": "com.example.castapp-debug-44:/layout_spinner_font_size_dropdown_item.xml.flat", "source": "com.example.castapp-main-46:/layout/spinner_font_size_dropdown_item.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_add.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_add.xml"}, {"merged": "com.example.castapp-debug-44:/layout_item_font_setting.xml.flat", "source": "com.example.castapp-main-46:/layout/item_font_setting.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_line_spacing_settings.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_line_spacing_settings.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_bottom_sheet_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/bottom_sheet_background.xml"}, {"merged": "com.example.castapp-debug-44:/layout_item_layer.xml.flat", "source": "com.example.castapp-main-46:/layout/item_layer.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_dialog_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/dialog_background.xml"}, {"merged": "com.example.castapp-debug-44:/layout_spinner_font_size_item.xml.flat", "source": "com.example.castapp-main-46:/layout/spinner_font_size_item.xml"}, {"merged": "com.example.castapp-debug-44:/layout_spinner_letter_spacing_item.xml.flat", "source": "com.example.castapp-main-46:/layout/spinner_letter_spacing_item.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_format_clear.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_format_clear.xml"}, {"merged": "com.example.castapp-debug-44:/layout_item_director_info.xml.flat", "source": "com.example.castapp-main-46:/layout/item_director_info.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_save.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_save.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_save_options.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_save_options.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_button_reset_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/button_reset_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_add_video.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_add_video.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_list_item_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/list_item_background.xml"}, {"merged": "com.example.castapp-debug-44:/layout_crop_control_buttons.xml.flat", "source": "com.example.castapp-main-46:/layout/crop_control_buttons.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_add_text.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_add_text.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_format_align_center.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_format_align_center.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_remote_receiver_control.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_remote_receiver_control.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_window_settings.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_window_settings.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_color_circle_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/color_circle_background.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_note_edit.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_note_edit.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_add_media.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_add_media.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_director.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_director.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_error.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_error.xml"}, {"merged": "com.example.castapp-debug-44:/layout_spinner_line_spacing_item.xml.flat", "source": "com.example.castapp-main-46:/layout/spinner_line_spacing_item.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_button_cancel_apply_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/button_cancel_apply_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_info.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_info.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_format_align_right.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_format_align_right.xml"}, {"merged": "com.example.castapp-debug-44:/layout_fragment_sender_tab.xml.flat", "source": "com.example.castapp-main-46:/layout/fragment_sender_tab.xml"}, {"merged": "com.example.castapp-debug-44:/layout_precision_control_panel.xml.flat", "source": "com.example.castapp-main-46:/layout/precision_control_panel.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_delete.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_delete.xml"}, {"merged": "com.example.castapp-debug-44:/layout_fragment_receiver_tab.xml.flat", "source": "com.example.castapp-main-46:/layout/fragment_receiver_tab.xml"}, {"merged": "com.example.castapp-debug-44:/layout_item_connection.xml.flat", "source": "com.example.castapp-main-46:/layout/item_connection.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_refresh.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_refresh.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_edit_text_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/edit_text_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_cast.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_cast.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_settings.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_settings.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_add_remote_device.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_add_remote_device.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_connection_status_indicator.xml.flat", "source": "com.example.castapp-main-46:/drawable/connection_status_indicator.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_floating_stopwatch_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/floating_stopwatch_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_stopwatch.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_stopwatch.xml"}, {"merged": "com.example.castapp-debug-44:/layout_spinner_line_spacing_dropdown_item.xml.flat", "source": "com.example.castapp-main-46:/layout/spinner_line_spacing_dropdown_item.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_item_selected_applied_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/item_selected_applied_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_close.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_close.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_add_media.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_add_media.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_spinner_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/spinner_background.xml"}, {"merged": "com.example.castapp-debug-44:/layout_item_director_layout.xml.flat", "source": "com.example.castapp-main-46:/layout/item_director_layout.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_remote_control_manager.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_remote_control_manager.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_add_picture.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_add_picture.xml"}, {"merged": "com.example.castapp-debug-44:/layout_spinner_text_alignment_dropdown_item.xml.flat", "source": "com.example.castapp-main-46:/layout/spinner_text_alignment_dropdown_item.xml"}, {"merged": "com.example.castapp-debug-44:/layout_item_remote_receiver.xml.flat", "source": "com.example.castapp-main-46:/layout/item_remote_receiver.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_count_badge_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/count_badge_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_updated.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_updated.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_ic_window_settings.xml.flat", "source": "com.example.castapp-main-46:/drawable/ic_window_settings.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_crop_control_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/crop_control_background.xml"}, {"merged": "com.example.castapp-debug-44:/layout_item_letter_spacing.xml.flat", "source": "com.example.castapp-main-46:/layout/item_letter_spacing.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_palette.xml.flat", "source": "com.example.castapp-main-46:/drawable/palette.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_precision_control_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/precision_control_background.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_button_delete_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/button_delete_background.xml"}, {"merged": "com.example.castapp-debug-44:/layout_activity_main.xml.flat", "source": "com.example.castapp-main-46:/layout/activity_main.xml"}, {"merged": "com.example.castapp-debug-44:/layout_dialog_layer_manager.xml.flat", "source": "com.example.castapp-main-46:/layout/dialog_layer_manager.xml"}, {"merged": "com.example.castapp-debug-44:/drawable_button_cancel_background.xml.flat", "source": "com.example.castapp-main-46:/drawable/button_cancel_background.xml"}]