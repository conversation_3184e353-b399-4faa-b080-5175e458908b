package com.example.castapp.manager.windowsettings

import android.app.Activity
import android.graphics.BitmapFactory
import android.graphics.RectF
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.view.View
import android.widget.FrameLayout
import com.example.castapp.database.entity.WindowLayoutItemEntity
import com.example.castapp.manager.LayoutManager
import com.example.castapp.manager.StateManager
import com.example.castapp.manager.WindowSettingsManager
import com.example.castapp.service.ReceivingService
import com.example.castapp.ui.windowsettings.TransformHandler
import com.example.castapp.utils.AppLog
import com.example.castapp.utils.MediaFileManager
import com.example.castapp.utils.NoteManager
import com.example.castapp.utils.TextFormatManager
import com.example.castapp.utils.TextSizeManager

/**
 * 窗口创建模块
 * 负责投屏窗口的创建和初始化逻辑
 */
class WindowCreationModule(
    private val dataModule: WindowDataModule
) {
    
    // 变换值变化回调
    private var transformValueChangeCallback: ((String, Float, Float, Float, Float) -> Unit)? = null
    
    /**
     * 设置变换值变化回调
     */
    fun setTransformValueChangeCallback(callback: (String, Float, Float, Float, Float) -> Unit) {
        this.transformValueChangeCallback = callback
    }
    
    /**
     * 计算窗口尺寸（基于发送端分辨率）
     */
    fun calculateWindowSize(originalWidth: Int, originalHeight: Int): Pair<Int, Int> {
        // 使用0.4倍缩放比例计算窗口尺寸
        val scaleFactor = 0.4f
        val windowWidth = (originalWidth * scaleFactor).toInt()
        val windowHeight = (originalHeight * scaleFactor).toInt()

        AppLog.d("计算窗口尺寸: 原始=${originalWidth}x${originalHeight}, 窗口=${windowWidth}x${windowHeight}")
        return Pair(windowWidth, windowHeight)
    }
    
    /**
     * 为连接创建投屏窗口
     */
    fun createWindowForConnection(connectionId: String) {
        val activity = dataModule.getCurrentActivity() ?: return
        val container = dataModule.getSurfaceContainer() ?: return
        
        activity.runOnUiThread {
            try {
                if (dataModule.containsWindow(connectionId)) {
                    AppLog.d("连接 $connectionId 的投屏窗口已存在")
                    return@runOnUiThread
                }
                
                val (windowWidth, windowHeight) = dataModule.getScreenResolution(connectionId)?.let { (width, height) ->
                    calculateWindowSize(width, height)
                } ?: Pair(TransformHandler.DEFAULT_WINDOW_WIDTH, TransformHandler.DEFAULT_WINDOW_HEIGHT)
                
                // 检查是否有应用的布局，如果有则使用布局参数创建窗口
                checkAndCreateWindowWithLayout(connectionId, windowWidth, windowHeight, activity, container)
                
            } catch (e: Exception) {
                AppLog.e("创建投屏窗口失败: $connectionId", e)
            }
        }
    }

    /**
     * 🏷️ 同步单个布局项的备注信息到SharedPreferences
     */
    private fun syncNoteToSharedPreferences(
        layoutItem: WindowLayoutItemEntity,
        activity: Activity
    ) {
        try {
            // 如果布局项中有备注信息，同步到SharedPreferences
            if (!layoutItem.note.isNullOrBlank() && layoutItem.note != "无") {
                val noteManager = NoteManager(activity)
                val success = noteManager.saveNote(layoutItem.deviceId, layoutItem.note)
                if (success) {
                    AppLog.d("【首次投屏-备注同步】成功同步备注: ${layoutItem.deviceId} -> ${layoutItem.note}")
                } else {
                    AppLog.w("【首次投屏-备注同步】同步备注失败: ${layoutItem.deviceId}")
                }
            } else {
                AppLog.d("【首次投屏-备注同步】布局项无备注信息: ${layoutItem.deviceId}")
            }
        } catch (e: Exception) {
            AppLog.e("【首次投屏-备注同步】同步备注信息失败: ${layoutItem.deviceId}", e)
        }
    }

    /**
     * 检查并使用布局参数创建窗口
     */
    private fun checkAndCreateWindowWithLayout(
        connectionId: String,
        windowWidth: Int,
        windowHeight: Int,
        activity: Activity,
        container: FrameLayout
    ) {
        val layoutManager = LayoutManager.getInstance()

        // 获取当前应用的布局
        layoutManager.getCurrentAppliedLayout { appliedLayout ->
            if (appliedLayout != null) {
                AppLog.d("【自动布局】检测到应用的布局: ${appliedLayout.layoutName}, 开始获取布局参数")

                // 获取布局详情
                layoutManager.getLayoutDetails(appliedLayout.id) { success, layoutItems, message ->
                    if (success && layoutItems != null) {
                        // 查找匹配当前连接ID的布局项
                        val matchingLayoutItem = layoutItems.find { it.deviceId == connectionId }

                        if (matchingLayoutItem != null) {
                            AppLog.d("【自动布局】找到匹配的布局参数: $connectionId, 使用布局参数创建窗口")

                            // 使用布局参数创建窗口
                            createWindowWithLayoutParameters(
                                connectionId, windowWidth, windowHeight,
                                activity, container, matchingLayoutItem
                            )
                        } else {
                            AppLog.d("【自动布局】未找到匹配的布局参数: $connectionId, 使用默认参数创建窗口")

                            // 使用默认参数创建窗口
                            createWindowWithDefaultParameters(
                                connectionId, windowWidth, windowHeight,
                                activity, container
                            )
                        }
                    } else {
                        AppLog.w("【自动布局】获取布局详情失败: $message, 使用默认参数创建窗口")

                        // 使用默认参数创建窗口
                        createWindowWithDefaultParameters(
                            connectionId, windowWidth, windowHeight,
                            activity, container
                        )
                    }
                }
            } else {
                AppLog.d("【自动布局】当前没有应用的布局，使用默认参数创建窗口")

                // 使用默认参数创建窗口
                createWindowWithDefaultParameters(
                    connectionId, windowWidth, windowHeight,
                    activity, container
                )
            }
        }
    }
    
    /**
     * 使用布局参数创建窗口
     */
    private fun createWindowWithLayoutParameters(
        connectionId: String,
        windowWidth: Int,
        windowHeight: Int,
        activity: Activity,
        container: FrameLayout,
        layoutItem: WindowLayoutItemEntity
    ) {
        try {
            // 🏷️ 同步备注信息到SharedPreferences
            syncNoteToSharedPreferences(layoutItem, activity)

            // 使用布局参数创建TransformHandler
            val transformHandler = TransformHandler(activity, initialWidth = windowWidth, initialHeight = windowHeight).apply {
                // 直接设置布局参数，避免默认位置闪烁
                x = layoutItem.positionX
                y = layoutItem.positionY
            }

            // 设置连接
            transformHandler.setupForConnection(connectionId)

            // 设置Surface回调
            transformHandler.setSurfaceAvailableCallback { surface, connId ->
                ReceivingService.setSurfaceForConnection(connId, surface)
            }

            // 设置设备信息（用于裁剪模式显示）
            transformHandler.setDeviceInfo(
                deviceName = layoutItem.deviceName,
                ipAddress = layoutItem.ipAddress,
                port = layoutItem.port
            )

            // 设置回调
            setupTransformHandlerCallbacks(transformHandler)

            // 如果有裁剪参数，先隐藏窗口避免闪烁
            val hasCropParameters = layoutItem.cropRect != null
            if (hasCropParameters) {
                transformHandler.alpha = 0f
                AppLog.d("【自动布局】检测到裁剪参数，先隐藏窗口避免闪烁")
            }

            // 应用布局参数（除裁剪外）
            applyLayoutParametersExceptCrop(transformHandler, layoutItem)

            // 🎯 双重偏移修复：延迟应用裁剪参数，并在应用后重新设置位置
            if (hasCropParameters) {
                transformHandler.postDelayed({
                    // 先应用裁剪参数
                    applyCropParametersDelayed(transformHandler, layoutItem)

                    // 🎯 关键修复：裁剪应用后，重新设置位置以应用双重偏移修复
                    transformHandler.setPrecisionTransform(
                        x = layoutItem.positionX,
                        y = layoutItem.positionY,
                        scale = layoutItem.scaleFactor,
                        rotation = layoutItem.rotationAngle
                    )
                    AppLog.d("【自动布局】裁剪应用后重新设置位置，应用双重偏移修复")

                    // 🎯 透明度修复：使用正确的透明度设置方法，确保窗口管理器能正确获取透明度值
                    transformHandler.setWindowAlpha(layoutItem.alpha)
                    AppLog.d("【自动布局】裁剪参数应用完成，恢复窗口显示，透明度=${layoutItem.alpha}")
                }, 100) // 延迟100ms确保TextureView完全初始化
            }

            // 添加到容器
            val layoutParams = FrameLayout.LayoutParams(windowWidth, windowHeight)
            container.addView(transformHandler, layoutParams)
            dataModule.addWindowMapping(connectionId, transformHandler)

            AppLog.d("【自动布局】使用布局参数创建窗口完成: $connectionId, 位置(${layoutItem.positionX}, ${layoutItem.positionY}), 层级索引: ${layoutItem.orderIndex}")

            // 🏷️ 延迟刷新对话框以显示同步后的备注信息
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                // 这里需要通过回调通知刷新，但WindowCreationModule没有直接的对话框引用
                // 备注信息已经同步到SharedPreferences，当用户打开窗口管理时会自动显示
                AppLog.d("【首次投屏-备注同步】窗口创建完成，备注信息已同步")
            }, 200)

        } catch (e: Exception) {
            AppLog.e("【自动布局】使用布局参数创建窗口失败", e)

            // 失败时回退到默认创建方式
            createWindowWithDefaultParameters(connectionId, windowWidth, windowHeight, activity, container)
        }
    }
    
    /**
     * 使用默认参数创建窗口
     */
    private fun createWindowWithDefaultParameters(
        connectionId: String,
        windowWidth: Int,
        windowHeight: Int,
        activity: Activity,
        container: FrameLayout
    ) {
        val transformHandler = TransformHandler(activity, initialWidth = windowWidth, initialHeight = windowHeight).apply {
            x = 50f
            y = 100f
        }

        transformHandler.setupForConnection(connectionId)

        // 设置Surface回调
        transformHandler.setSurfaceAvailableCallback { surface, connId ->
            ReceivingService.setSurfaceForConnection(connId, surface)
        }

        // 设置设备信息（用于裁剪模式显示）
        val stateManager = StateManager.getInstance(activity.application)
        val connection = stateManager.findConnectionById(connectionId)
        val deviceName = stateManager.getConnectionDeviceName(connectionId)
        if (connection != null) {
            transformHandler.setDeviceInfo(
                deviceName = deviceName,
                ipAddress = connection.ipAddress,
                port = connection.port
            )
        }

        // 设置回调
        setupTransformHandlerCallbacks(transformHandler)

        val layoutParams = FrameLayout.LayoutParams(windowWidth, windowHeight)
        container.addView(transformHandler, layoutParams)
        dataModule.addWindowMapping(connectionId, transformHandler)

        AppLog.d("使用默认参数创建窗口完成: $connectionId")
    }
    
    /**
     * 设置TransformHandler回调
     */
    private fun setupTransformHandlerCallbacks(transformHandler: TransformHandler) {
        // 设置裁剪模式变化回调
        transformHandler.setCropModeChangeCallback { isEnabled ->
            // 移除自动刷新，避免用户手动排序被重置
        }

        // 设置变换变化回调
        transformHandler.setTransformChangeCallback { connId, x, y, scale, rotation ->
            // 通知精准控制面板更新数值显示
            transformValueChangeCallback?.invoke(connId, x, y, scale, rotation)
        }
    }

    /**
     * 应用布局参数（除裁剪外）
     */
    private fun applyLayoutParametersExceptCrop(
        transformHandler: TransformHandler,
        layoutItem: WindowLayoutItemEntity
    ) {
        try {
            // 应用变换参数（位置已在创建时设置）
            transformHandler.setPrecisionTransform(
                x = layoutItem.positionX,
                y = layoutItem.positionY,
                scale = layoutItem.scaleFactor,
                rotation = layoutItem.rotationAngle
            )

            // 恢复视觉效果相关的功能状态
            transformHandler.setMirrorEnabled(layoutItem.isMirrored)

            // 🎯 恢复横屏开关状态（首次投屏自动布局）
            if (isRealCastWindow(layoutItem.deviceId)) {
                restoreLandscapeModeStateForNewWindow(layoutItem.deviceId, layoutItem.isLandscapeModeEnabled)
                AppLog.d("【自动布局】横屏状态设置: ${layoutItem.deviceId}, isLandscapeModeEnabled=${layoutItem.isLandscapeModeEnabled}")
            }

            // 应用视觉效果参数
            transformHandler.setCornerRadius(layoutItem.cornerRadius)

            // 恢复边框相关状态
            transformHandler.setBorderEnabled(layoutItem.isBorderEnabled)
            transformHandler.setBorderColor(layoutItem.borderColor)
            transformHandler.setBorderWidth(layoutItem.borderWidth)

            // 🎯 透明度修复：使用正确的透明度设置方法
            val hasCropParameters = layoutItem.cropRect != null
            if (!hasCropParameters) {
                transformHandler.setWindowAlpha(layoutItem.alpha)
            }

            transformHandler.visibility = if (layoutItem.isVisible) android.view.View.VISIBLE else android.view.View.GONE

            AppLog.d("【自动布局】基础参数应用完成: 位置(${layoutItem.positionX}, ${layoutItem.positionY}), 缩放=${layoutItem.scaleFactor}, 旋转=${layoutItem.rotationAngle}, 边框=${layoutItem.isBorderEnabled}")

        } catch (e: Exception) {
            AppLog.e("【自动布局】应用基础参数失败", e)
        }
    }

    /**
     * 🎯 判断是否为真实的投屏窗口（非本地窗口）
     */
    private fun isRealCastWindow(connectionId: String): Boolean {
        return !connectionId.let { id ->
            id == "front_camera" || id == "rear_camera" ||
            id.startsWith("video_") || id.startsWith("image_") ||
            id.startsWith("text_")
        }
    }

    /**
     * 🎯 为新创建的窗口恢复横屏模式状态
     * 用于首次投屏时的自动布局应用
     */
    private fun restoreLandscapeModeStateForNewWindow(connectionId: String, isEnabled: Boolean) {
        try {
            val windowSettingsManager = WindowSettingsManager.getInstance()

            // 🎯 关键修复：延迟发送横屏控制消息，确保WebSocket连接完全建立
            // 先更新本地状态（不发送消息）
            windowSettingsManager.setLandscapeModeStateOnly(connectionId, isEnabled)

            // 延迟发送控制消息，等待WebSocket连接建立
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                try {
                    // 现在发送横屏控制消息
                    windowSettingsManager.sendLandscapeModeControlMessagePublic(connectionId, isEnabled)
                    AppLog.d("【自动布局】延迟发送横屏控制消息: $connectionId -> $isEnabled")
                } catch (e: Exception) {
                    AppLog.e("【自动布局】延迟发送横屏控制消息失败: $connectionId", e)
                }
            }, 2000) // 延迟2秒，确保WebSocket连接完全建立

            AppLog.d("【自动布局】横屏模式状态已恢复（延迟发送）: $connectionId -> $isEnabled")

        } catch (e: Exception) {
            AppLog.e("【自动布局】恢复横屏模式状态失败: $connectionId", e)
        }
    }

    /**
     * 延迟应用裁剪参数
     */
    private fun applyCropParametersDelayed(
        transformHandler: TransformHandler,
        layoutItem: WindowLayoutItemEntity
    ) {
        try {
            layoutItem.cropRect?.let { cropRectJson ->
                try {
                    // 解析裁剪区域JSON
                    val left = cropRectJson.substringAfter("\"left\":").substringBefore(",").toFloat()
                    val top = cropRectJson.substringAfter("\"top\":").substringBefore(",").toFloat()
                    val right = cropRectJson.substringAfter("\"right\":").substringBefore(",").toFloat()
                    val bottom = cropRectJson.substringAfter("\"bottom\":").substringBefore("}").toFloat()
                    val cropRect = RectF(left, top, right, bottom)

                    // 延迟应用裁剪参数，确保TextureView已完全初始化
                    transformHandler.setCropRectRatio(cropRect)
                    AppLog.d("【自动布局】延迟应用裁剪区域成功: $cropRectJson")
                } catch (e: Exception) {
                    AppLog.w("【自动布局】解析裁剪区域失败: $cropRectJson", e)
                }
            }
        } catch (e: Exception) {
            AppLog.e("【自动布局】延迟应用裁剪参数失败", e)
        }
    }

    /**
     * 创建摄像头窗口
     */
    fun createCameraWindow(cameraId: String, cameraName: String) {
        val activity = dataModule.getCurrentActivity() ?: return
        val container = dataModule.getSurfaceContainer() ?: return

        activity.runOnUiThread {
            try {
                // 计算摄像头窗口尺寸（接收端屏幕分辨率 × 0.4）
                val (windowWidth, windowHeight) = calculateCameraWindowSize()

                // 检查是否有应用的布局，如果有则使用布局参数创建摄像头窗口
                checkAndCreateCameraWindowWithLayout(cameraId, cameraName, windowWidth, windowHeight, activity, container)

            } catch (e: Exception) {
                AppLog.e("创建摄像头窗口失败: $cameraName", e)
            }
        }
    }

    /**
     * 🎥 新增：使用指定参数创建摄像头窗口
     * 用于远程同步时直接应用遥控端参数，避免窗口闪烁
     */
    fun createCameraWindowWithParameters(
        cameraId: String,
        cameraName: String,
        windowData: Map<String, Any>
    ) {
        val activity = dataModule.getCurrentActivity() ?: return
        val container = dataModule.getSurfaceContainer() ?: return

        activity.runOnUiThread {
            try {
                AppLog.d("🎥 开始使用参数创建摄像头窗口: $cameraName (ID: $cameraId)")

                // 计算摄像头窗口尺寸（接收端屏幕分辨率 × 0.4）
                val (windowWidth, windowHeight) = calculateCameraWindowSize()

                // 直接使用参数创建摄像头窗口，不检查布局
                createCameraWindowWithRemoteParameters(
                    cameraId, cameraName, windowWidth, windowHeight,
                    activity, container, windowData
                )

            } catch (e: Exception) {
                AppLog.e("🎥 使用参数创建摄像头窗口失败: $cameraName", e)
            }
        }
    }

    /**
     * 创建媒体窗口（视频/图片）
     */
    fun createMediaWindow(mediaId: String, mediaType: String, fileName: String, uri: Uri, contentType: String) {
        val activity = dataModule.getCurrentActivity() ?: return
        val container = dataModule.getSurfaceContainer() ?: return

        activity.runOnUiThread {
            try {
                // 📁 保存媒体文件信息到MediaFileManager
                val mediaFileManager = MediaFileManager(activity)
                mediaFileManager.saveMediaFileInfo(mediaId, fileName, uri, contentType, mediaType)

                // 计算媒体窗口尺寸
                val (windowWidth, windowHeight) = calculateMediaWindowSize(uri, contentType, activity)

                // 检查是否有应用的布局，如果有则使用布局参数创建媒体窗口
                checkAndCreateMediaWindowWithLayout(mediaId, mediaType, fileName, uri, contentType, windowWidth, windowHeight, activity, container)

            } catch (e: Exception) {
                AppLog.e("创建媒体窗口失败: $mediaType", e)
            }
        }
    }

    /**
     * 创建文本窗口
     */
    fun createTextWindow(textId: String, textContent: String) {
        val activity = dataModule.getCurrentActivity() ?: return
        val container = dataModule.getSurfaceContainer() ?: return

        activity.runOnUiThread {
            try {
                // 计算文本窗口尺寸（使用TextSizeManager）
                val (windowWidth, windowHeight) = calculateTextWindowSize(textId)

                // 检查是否有应用的布局，如果有则使用布局参数创建文本窗口
                checkAndCreateTextWindowWithLayout(textId, textContent, windowWidth, windowHeight, activity, container)

            } catch (e: Exception) {
                AppLog.e("创建文本窗口失败: ID=$textId", e)
            }
        }
    }

    /**
     * 检查并使用布局参数创建摄像头窗口
     */
    private fun checkAndCreateCameraWindowWithLayout(
        cameraId: String,
        cameraName: String,
        windowWidth: Int,
        windowHeight: Int,
        activity: Activity,
        container: FrameLayout
    ) {
        val layoutManager = LayoutManager.getInstance()

        // 获取当前应用的布局
        layoutManager.getCurrentAppliedLayout { appliedLayout ->
            if (appliedLayout != null) {
                AppLog.d("【摄像头自动布局】检测到应用的布局: ${appliedLayout.layoutName}, 开始获取布局参数")

                // 获取布局详情
                layoutManager.getLayoutDetails(appliedLayout.id) { success, layoutItems, message ->
                    if (success && layoutItems != null) {
                        // 查找匹配当前摄像头ID的布局项
                        val matchingLayoutItem = layoutItems.find { it.deviceId == cameraId }

                        if (matchingLayoutItem != null) {
                            AppLog.d("【摄像头自动布局】找到匹配的布局参数: $cameraId, 使用布局参数创建摄像头窗口")

                            // 使用布局参数创建摄像头窗口
                            createCameraWindowWithLayoutParameters(
                                cameraId, cameraName, windowWidth, windowHeight,
                                activity, container, matchingLayoutItem
                            )
                        } else {
                            AppLog.d("【摄像头自动布局】未找到匹配的布局参数: $cameraId, 使用默认参数创建摄像头窗口")

                            // 使用默认参数创建摄像头窗口
                            createCameraWindowWithDefaultParameters(
                                cameraId, cameraName, windowWidth, windowHeight,
                                activity, container
                            )
                        }
                    } else {
                        AppLog.w("【摄像头自动布局】获取布局详情失败: $message, 使用默认参数创建摄像头窗口")

                        // 获取布局详情失败，使用默认参数创建摄像头窗口
                        createCameraWindowWithDefaultParameters(
                            cameraId, cameraName, windowWidth, windowHeight,
                            activity, container
                        )
                    }
                }
            } else {
                AppLog.d("【摄像头自动布局】未检测到应用的布局，使用默认参数创建摄像头窗口")

                // 没有应用的布局，使用默认参数创建摄像头窗口
                createCameraWindowWithDefaultParameters(
                    cameraId, cameraName, windowWidth, windowHeight,
                    activity, container
                )
            }
        }
    }

    /**
     * 计算摄像头窗口尺寸
     */
    private fun calculateCameraWindowSize(): Pair<Int, Int> {
        // 获取屏幕分辨率
        val activity = dataModule.getCurrentActivity() ?: return Pair(400, 600)
        val displayMetrics = activity.resources.displayMetrics
        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels

        // 计算窗口尺寸（屏幕分辨率 × 0.4）
        val windowWidth = (screenWidth * 0.4).toInt()
        val windowHeight = (screenHeight * 0.4).toInt()

        AppLog.d("计算摄像头窗口尺寸: 屏幕${screenWidth}x${screenHeight} -> 窗口${windowWidth}x${windowHeight}")

        return Pair(windowWidth, windowHeight)
    }

    /**
     * 使用布局参数创建摄像头窗口
     */
    private fun createCameraWindowWithLayoutParameters(
        cameraId: String,
        cameraName: String,
        windowWidth: Int,
        windowHeight: Int,
        activity: Activity,
        container: FrameLayout,
        layoutItem: WindowLayoutItemEntity
    ) {
        try {
            // 🏷️ 同步备注信息到SharedPreferences
            syncNoteToSharedPreferences(layoutItem, activity)

            // 使用布局参数创建TransformHandler
            val transformHandler = TransformHandler(activity, initialWidth = windowWidth, initialHeight = windowHeight).apply {
                // 直接设置布局参数，避免默认位置闪烁
                x = layoutItem.positionX
                y = layoutItem.positionY
            }

            // 设置摄像头连接
            transformHandler.setupForCameraConnection(cameraId, cameraName)

            // 设置设备信息
            transformHandler.setDeviceInfo(
                deviceName = cameraName,
                ipAddress = "本地设备",
                port = 0
            )

            // 设置回调
            setupTransformHandlerCallbacks(transformHandler)

            // 检查是否有裁剪参数
            val hasCropParameters = layoutItem.cropRect != null
            if (hasCropParameters) {
                transformHandler.alpha = 0f
                AppLog.d("【摄像头自动布局】检测到裁剪参数，先隐藏窗口避免闪烁")
            }

            // 应用布局参数（除裁剪外）
            applyLayoutParametersExceptCrop(transformHandler, layoutItem)

            // 🎯 双重偏移修复：延迟应用裁剪参数，并在应用后重新设置位置
            if (hasCropParameters) {
                transformHandler.postDelayed({
                    // 先应用裁剪参数
                    applyCropParametersDelayed(transformHandler, layoutItem)

                    // 🎯 关键修复：裁剪应用后，重新设置位置以应用双重偏移修复
                    transformHandler.setPrecisionTransform(
                        x = layoutItem.positionX,
                        y = layoutItem.positionY,
                        scale = layoutItem.scaleFactor,
                        rotation = layoutItem.rotationAngle
                    )
                    AppLog.d("【摄像头自动布局】裁剪应用后重新设置位置，应用双重偏移修复")

                    // 🎯 透明度修复：使用正确的透明度设置方法，确保窗口管理器能正确获取透明度值
                    transformHandler.setWindowAlpha(layoutItem.alpha)
                    AppLog.d("【摄像头自动布局】裁剪参数应用完成，恢复窗口显示，透明度=${layoutItem.alpha}")
                }, 100) // 延迟100ms确保TextureView完全初始化
            }

            // 添加到容器
            val layoutParams = FrameLayout.LayoutParams(windowWidth, windowHeight)
            container.addView(transformHandler, layoutParams)
            dataModule.addWindowMapping(cameraId, transformHandler)

            AppLog.d("【摄像头自动布局】使用布局参数创建摄像头窗口完成: $cameraName (ID: $cameraId), 位置(${layoutItem.positionX}, ${layoutItem.positionY}), 层级索引: ${layoutItem.orderIndex}")

        } catch (e: Exception) {
            AppLog.e("【摄像头自动布局】使用布局参数创建摄像头窗口失败", e)

            // 失败时回退到默认创建方式
            createCameraWindowWithDefaultParameters(cameraId, cameraName, windowWidth, windowHeight, activity, container)
        }
    }

    /**
     * 使用默认参数创建摄像头窗口
     */
    private fun createCameraWindowWithDefaultParameters(
        cameraId: String,
        cameraName: String,
        windowWidth: Int,
        windowHeight: Int,
        activity: Activity,
        container: FrameLayout
    ) {
        try {
            // 创建TransformHandler
            val transformHandler = TransformHandler(activity, initialWidth = windowWidth, initialHeight = windowHeight)

            // 设置摄像头连接
            transformHandler.setupForCameraConnection(cameraId, cameraName)

            // 设置设备信息
            transformHandler.setDeviceInfo(
                deviceName = cameraName,
                ipAddress = "本地设备",
                port = 0
            )

            // 设置回调
            setupTransformHandlerCallbacks(transformHandler)

            // 添加到容器
            val layoutParams = FrameLayout.LayoutParams(windowWidth, windowHeight)
            container.addView(transformHandler, layoutParams)
            dataModule.addWindowMapping(cameraId, transformHandler)

            AppLog.d("摄像头窗口创建完成: $cameraName (ID: $cameraId), 尺寸: ${windowWidth}x${windowHeight}")

        } catch (e: Exception) {
            AppLog.e("创建摄像头窗口失败: $cameraName", e)
        }
    }

    /**
     * 🎥 使用远程参数创建摄像头窗口
     * 直接应用遥控端发送的所有参数，避免窗口闪烁和参数丢失
     */
    private fun createCameraWindowWithRemoteParameters(
        cameraId: String,
        cameraName: String,
        windowWidth: Int,
        windowHeight: Int,
        activity: Activity,
        container: FrameLayout,
        windowData: Map<String, Any>
    ) {
        try {
            AppLog.d("🎥 开始使用远程参数创建摄像头窗口: $cameraName")

            // 提取参数
            val positionX = (windowData["positionX"] as? Number)?.toFloat() ?: 100f
            val positionY = (windowData["positionY"] as? Number)?.toFloat() ?: 100f
            val scaleFactor = (windowData["scaleFactor"] as? Number)?.toFloat() ?: 1f
            val rotationAngle = (windowData["rotationAngle"] as? Number)?.toFloat() ?: 0f
            val alpha = (windowData["alpha"] as? Number)?.toFloat() ?: 1f
            val cornerRadius = (windowData["cornerRadius"] as? Number)?.toFloat() ?: 16f
            val isVisible = windowData["isVisible"] as? Boolean ?: true
            val isMirrored = windowData["isMirrored"] as? Boolean ?: false

            // 边框参数
            val isBorderEnabled = windowData["isBorderEnabled"] as? Boolean ?: false
            val borderColor = (windowData["borderColor"] as? Number)?.toInt() ?: 0xFFFF0000.toInt()
            val borderWidth = (windowData["borderWidth"] as? Number)?.toFloat() ?: 4f

            AppLog.d("🎥 远程参数解析完成:")
            AppLog.d("  位置: ($positionX, $positionY)")
            AppLog.d("  变换: 缩放=$scaleFactor, 旋转=${rotationAngle}°")
            AppLog.d("  视觉: 透明度=$alpha, 圆角=${cornerRadius}dp, 可见=$isVisible, 镜像=$isMirrored")
            AppLog.d("  边框: 启用=$isBorderEnabled, 颜色=${String.format("#%08X", borderColor)}, 宽度=${borderWidth}dp")

            // 创建TransformHandler并直接设置位置
            val transformHandler = TransformHandler(activity, initialWidth = windowWidth, initialHeight = windowHeight).apply {
                x = positionX
                y = positionY
            }

            // 设置摄像头连接
            transformHandler.setupForCameraConnection(cameraId, cameraName)

            // 设置设备信息
            transformHandler.setDeviceInfo(
                deviceName = cameraName,
                ipAddress = "本地设备",
                port = 0
            )

            // 设置回调
            setupTransformHandlerCallbacks(transformHandler)

            // 🎥 应用所有参数
            // 应用变换参数
            transformHandler.setPrecisionTransform(
                x = positionX,
                y = positionY,
                scale = scaleFactor,
                rotation = rotationAngle
            )

            // 应用视觉参数
            transformHandler.setWindowAlpha(alpha)
            transformHandler.setCornerRadius(cornerRadius)
            transformHandler.setMirrorEnabled(isMirrored)

            // 应用边框参数（延迟创建机制已在TransformRenderer中实现）
            transformHandler.setBorderEnabled(isBorderEnabled)
            if (isBorderEnabled) {
                transformHandler.setBorderColor(borderColor)
                transformHandler.setBorderWidth(borderWidth)
            }

            // 添加到容器
            val layoutParams = FrameLayout.LayoutParams(windowWidth, windowHeight)
            container.addView(transformHandler, layoutParams)
            dataModule.addWindowMapping(cameraId, transformHandler)

            // 设置可见性
            transformHandler.visibility = if (isVisible) View.VISIBLE else View.GONE

            AppLog.d("🎥 使用远程参数创建摄像头窗口完成: $cameraName (ID: $cameraId)")
            AppLog.d("🎥 最终状态: 位置($positionX, $positionY), 缩放=$scaleFactor, 旋转=${rotationAngle}°, 边框=$isBorderEnabled")

        } catch (e: Exception) {
            AppLog.e("🎥 使用远程参数创建摄像头窗口失败: $cameraName", e)

            // 失败时回退到默认创建方式
            createCameraWindowWithDefaultParameters(cameraId, cameraName, windowWidth, windowHeight, activity, container)
        }
    }

    // ==================== 媒体窗口创建方法 ====================

    /**
     * 计算媒体窗口尺寸（视频/图片分辨率 × 0.4）（公开方法，供WindowLayoutModule调用）
     */
    fun calculateMediaWindowSize(uri: Uri, contentType: String, activity: Activity): Pair<Int, Int> {
        return try {
            when (contentType) {
                "video" -> {
                    // 获取视频分辨率
                    val (videoWidth, videoHeight) = getVideoResolution(uri, activity)
                    val windowWidth = (videoWidth * 0.4).toInt()
                    val windowHeight = (videoHeight * 0.4).toInt()
                    AppLog.d("【视频窗口】使用视频分辨率计算: ${videoWidth}x${videoHeight} -> ${windowWidth}x${windowHeight}")
                    Pair(windowWidth, windowHeight)
                }
                "image" -> {
                    // 获取图片分辨率
                    val (imageWidth, imageHeight) = getImageResolution(uri, activity)
                    val windowWidth = (imageWidth * 0.4).toInt()
                    val windowHeight = (imageHeight * 0.4).toInt()
                    AppLog.d("【图片窗口】使用图片分辨率计算: ${imageWidth}x${imageHeight} -> ${windowWidth}x${windowHeight}")
                    Pair(windowWidth, windowHeight)
                }
                else -> {
                    // 默认尺寸
                    val defaultWidth = (1920 * 0.4).toInt()
                    val defaultHeight = (1080 * 0.4).toInt()
                    AppLog.d("【媒体窗口】使用默认分辨率计算: 1920x1080 -> ${defaultWidth}x${defaultHeight}")
                    Pair(defaultWidth, defaultHeight)
                }
            }
        } catch (e: Exception) {
            AppLog.e("计算媒体窗口尺寸失败，使用默认尺寸", e)
            val defaultWidth = (1920 * 0.4).toInt()
            val defaultHeight = (1080 * 0.4).toInt()
            Pair(defaultWidth, defaultHeight)
        }
    }

    /**
     * 计算文本窗口尺寸
     */
    private fun calculateTextWindowSize(textId: String): Pair<Int, Int> {
        val activity = dataModule.getCurrentActivity() ?: return Pair(TextSizeManager.DEFAULT_WIDTH, TextSizeManager.DEFAULT_HEIGHT)

        // 使用TextSizeManager获取保存的尺寸或默认尺寸
        val textSizeManager = TextSizeManager(activity)
        val (windowWidth, windowHeight) = textSizeManager.getTextWindowSize(textId)

        AppLog.d("【文本窗口】使用TextSizeManager尺寸: ${windowWidth}x${windowHeight}")
        return Pair(windowWidth, windowHeight)
    }

    /**
     * 获取视频分辨率（考虑旋转信息）
     */
    private fun getVideoResolution(uri: Uri, activity: Activity): Pair<Int, Int> {
        return try {
            val retriever = MediaMetadataRetriever()
            retriever.setDataSource(activity, uri)

            val width = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH)?.toIntOrNull() ?: 1920
            val height = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT)?.toIntOrNull() ?: 1080

            // 获取视频旋转角度
            val rotationString = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION)
            val rotation = rotationString?.toIntOrNull() ?: 0

            retriever.release()

            // 如果视频旋转了90度或270度，需要交换宽高
            val finalWidth: Int
            val finalHeight: Int

            when (rotation) {
                90, 270 -> {
                    // 旋转90度或270度，交换宽高
                    finalWidth = height
                    finalHeight = width
                    AppLog.d("【视频分辨率】检测到旋转${rotation}度，交换宽高: ${width}x${height} -> ${finalWidth}x${finalHeight}")
                }
                else -> {
                    // 0度或180度，保持原始宽高
                    finalWidth = width
                    finalHeight = height
                    AppLog.d("【视频分辨率】无需旋转调整: ${finalWidth}x${finalHeight}")
                }
            }

            Pair(finalWidth, finalHeight)
        } catch (e: Exception) {
            AppLog.e("获取视频分辨率失败，使用默认值", e)
            Pair(1920, 1080)
        }
    }

    /**
     * 获取图片分辨率
     */
    private fun getImageResolution(uri: Uri, activity: Activity): Pair<Int, Int> {
        return try {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }

            activity.contentResolver.openInputStream(uri)?.use { inputStream ->
                BitmapFactory.decodeStream(inputStream, null, options)
            }

            val width = if (options.outWidth > 0) options.outWidth else 1920
            val height = if (options.outHeight > 0) options.outHeight else 1080

            Pair(width, height)
        } catch (e: Exception) {
            AppLog.e("获取图片分辨率失败，使用默认值", e)
            Pair(1920, 1080)
        }
    }

    /**
     * 检查并使用布局参数创建媒体窗口
     */
    private fun checkAndCreateMediaWindowWithLayout(
        mediaId: String,
        mediaType: String,
        fileName: String,
        uri: Uri,
        contentType: String,
        windowWidth: Int,
        windowHeight: Int,
        activity: Activity,
        container: FrameLayout
    ) {
        val layoutManager = LayoutManager.getInstance()

        // 获取当前应用的布局
        layoutManager.getCurrentAppliedLayout { appliedLayout ->
            if (appliedLayout != null) {
                AppLog.d("【媒体自动布局】检测到应用的布局: ${appliedLayout.layoutName}, 开始获取布局参数")

                // 获取布局详情
                layoutManager.getLayoutDetails(appliedLayout.id) { success, layoutItems, message ->
                    if (success && layoutItems != null) {
                        // 查找匹配当前媒体ID的布局项
                        val matchingLayoutItem = layoutItems.find { it.deviceId == mediaId }

                        if (matchingLayoutItem != null) {
                            AppLog.d("【媒体自动布局】找到匹配的布局参数: $mediaId, 使用布局参数创建媒体窗口")

                            // 使用布局参数创建媒体窗口
                            createMediaWindowWithLayoutParameters(
                                mediaId, mediaType, fileName, uri, contentType, windowWidth, windowHeight,
                                activity, container, matchingLayoutItem
                            )
                        } else {
                            AppLog.d("【媒体自动布局】未找到匹配的布局参数: $mediaId, 使用默认参数创建媒体窗口")

                            // 使用默认参数创建媒体窗口
                            createMediaWindowWithDefaultParameters(
                                mediaId, mediaType, fileName, uri, contentType, windowWidth, windowHeight,
                                activity, container
                            )
                        }
                    } else {
                        AppLog.w("【媒体自动布局】获取布局详情失败: $message, 使用默认参数创建媒体窗口")

                        // 获取布局详情失败，使用默认参数创建媒体窗口
                        createMediaWindowWithDefaultParameters(
                            mediaId, mediaType, fileName, uri, contentType, windowWidth, windowHeight,
                            activity, container
                        )
                    }
                }
            } else {
                AppLog.d("【媒体自动布局】未检测到应用的布局，使用默认参数创建媒体窗口")

                // 没有应用的布局，使用默认参数创建媒体窗口
                createMediaWindowWithDefaultParameters(
                    mediaId, mediaType, fileName, uri, contentType, windowWidth, windowHeight,
                    activity, container
                )
            }
        }
    }

    /**
     * 使用布局参数创建媒体窗口（公开方法，供WindowLayoutModule调用）
     */
    fun createMediaWindowWithLayoutParameters(
        mediaId: String,
        mediaType: String,
        fileName: String,
        uri: Uri,
        contentType: String,
        windowWidth: Int,
        windowHeight: Int,
        activity: Activity,
        container: FrameLayout,
        layoutItem: WindowLayoutItemEntity
    ) {
        try {
            // 🏷️ 同步备注信息到SharedPreferences
            syncNoteToSharedPreferences(layoutItem, activity)

            // 创建TransformHandler（不设置初始位置，避免不精确的位置设置）
            val transformHandler = TransformHandler(activity, initialWidth = windowWidth, initialHeight = windowHeight)

            // 设置媒体连接
            transformHandler.setupForMediaConnection(mediaId, fileName, uri, contentType)

            // 设置设备信息
            transformHandler.setDeviceInfo(
                deviceName = mediaType,
                ipAddress = "本地设备",
                port = 0
            )

            // 设置回调
            setupTransformHandlerCallbacks(transformHandler)

            // 检查是否有裁剪参数
            val hasCropParameters = layoutItem.cropRect != null
            if (hasCropParameters) {
                transformHandler.alpha = 0f
                AppLog.d("【媒体自动布局】检测到裁剪参数，先隐藏窗口避免闪烁")
            }

            // 应用布局参数（除裁剪外）
            applyLayoutParametersExceptCrop(transformHandler, layoutItem)

            // 🎯 双重偏移修复：延迟应用裁剪参数，并在应用后重新设置位置
            if (hasCropParameters) {
                transformHandler.postDelayed({
                    // 先应用裁剪参数
                    applyCropParametersDelayed(transformHandler, layoutItem)

                    // 🎯 关键修复：裁剪应用后，重新设置位置以应用双重偏移修复
                    transformHandler.setPrecisionTransform(
                        x = layoutItem.positionX,
                        y = layoutItem.positionY,
                        scale = layoutItem.scaleFactor,
                        rotation = layoutItem.rotationAngle
                    )
                    AppLog.d("【媒体自动布局】裁剪应用后重新设置位置，应用双重偏移修复")

                    // 🎯 透明度修复：使用正确的透明度设置方法，确保窗口管理器能正确获取透明度值
                    transformHandler.setWindowAlpha(layoutItem.alpha)
                    AppLog.d("【媒体自动布局】裁剪参数应用完成，恢复窗口显示，透明度=${layoutItem.alpha}")
                }, 100) // 延迟100ms确保MediaView完全初始化
            }

            // 添加到容器
            val layoutParams = FrameLayout.LayoutParams(windowWidth, windowHeight)
            container.addView(transformHandler, layoutParams)
            dataModule.addWindowMapping(mediaId, transformHandler)

            // 📁 立即应用精确的布局参数，确保位置准确
            transformHandler.post {
                // 应用完整的布局参数（使用与WindowLayoutModule相同的逻辑）
                applyCompleteLayoutParameters(transformHandler, layoutItem)
                AppLog.d("【媒体自动布局】精确布局参数已应用: $mediaId")
            }

            AppLog.d("【媒体自动布局】使用布局参数创建媒体窗口完成: $mediaType (ID: $mediaId), 位置(${layoutItem.positionX}, ${layoutItem.positionY}), 层级索引: ${layoutItem.orderIndex}")

        } catch (e: Exception) {
            AppLog.e("【媒体自动布局】使用布局参数创建媒体窗口失败", e)

            // 失败时回退到默认创建方式
            createMediaWindowWithDefaultParameters(mediaId, mediaType, fileName, uri, contentType, windowWidth, windowHeight, activity, container)
        }
    }

    /**
     * 使用默认参数创建媒体窗口
     */
    private fun createMediaWindowWithDefaultParameters(
        mediaId: String,
        mediaType: String,
        fileName: String,
        uri: Uri,
        contentType: String,
        windowWidth: Int,
        windowHeight: Int,
        activity: Activity,
        container: FrameLayout
    ) {
        try {
            // 创建TransformHandler
            val transformHandler = TransformHandler(activity, initialWidth = windowWidth, initialHeight = windowHeight)

            // 设置媒体连接
            transformHandler.setupForMediaConnection(mediaId, fileName, uri, contentType)

            // 设置设备信息
            transformHandler.setDeviceInfo(
                deviceName = mediaType,
                ipAddress = "本地设备",
                port = 0
            )

            // 设置回调
            setupTransformHandlerCallbacks(transformHandler)

            // 添加到容器
            val layoutParams = FrameLayout.LayoutParams(windowWidth, windowHeight)
            container.addView(transformHandler, layoutParams)
            dataModule.addWindowMapping(mediaId, transformHandler)

            AppLog.d("媒体窗口创建完成: $mediaType (ID: $mediaId), 尺寸: ${windowWidth}x${windowHeight}")

        } catch (e: Exception) {
            AppLog.e("创建媒体窗口失败: $mediaType", e)
        }
    }

    // ==================== 文本窗口创建方法 ====================

    /**
     * 检查并使用布局参数创建文本窗口
     */
    private fun checkAndCreateTextWindowWithLayout(
        textId: String,
        textContent: String,
        windowWidth: Int,
        windowHeight: Int,
        activity: Activity,
        container: FrameLayout
    ) {
        val layoutManager = LayoutManager.getInstance()

        // 获取当前应用的布局
        layoutManager.getCurrentAppliedLayout { appliedLayout ->
            if (appliedLayout != null) {
                AppLog.d("【文本自动布局】检测到应用的布局: ${appliedLayout.layoutName}, 开始获取布局参数")

                // 获取布局详情
                layoutManager.getLayoutDetails(appliedLayout.id) { success, layoutItems, message ->
                    if (success && layoutItems != null) {
                        // 查找匹配当前文本ID的布局项
                        val matchingLayoutItem = layoutItems.find { it.deviceId == textId }

                        if (matchingLayoutItem != null) {
                            AppLog.d("【文本自动布局】找到匹配的布局参数: $textId, 使用布局参数创建文本窗口")

                            // 使用布局参数创建文本窗口
                            createTextWindowWithLayoutParameters(
                                textId, textContent, windowWidth, windowHeight,
                                activity, container, matchingLayoutItem
                            )
                        } else {
                            AppLog.d("【文本自动布局】未找到匹配的布局参数: $textId, 使用默认参数创建文本窗口")

                            // 使用默认参数创建文本窗口
                            createTextWindowWithDefaultParameters(
                                textId, textContent, windowWidth, windowHeight,
                                activity, container
                            )
                        }
                    } else {
                        AppLog.w("【文本自动布局】获取布局详情失败: $message, 使用默认参数创建文本窗口")

                        // 获取布局详情失败，使用默认参数创建文本窗口
                        createTextWindowWithDefaultParameters(
                            textId, textContent, windowWidth, windowHeight,
                            activity, container
                        )
                    }
                }
            } else {
                AppLog.d("【文本自动布局】未检测到应用的布局，使用默认参数创建文本窗口")

                // 没有应用的布局，使用默认参数创建文本窗口
                createTextWindowWithDefaultParameters(
                    textId, textContent, windowWidth, windowHeight,
                    activity, container
                )
            }
        }
    }

    /**
     * 使用布局参数创建文本窗口
     */
    private fun createTextWindowWithLayoutParameters(
        textId: String,
        textContent: String,
        windowWidth: Int,
        windowHeight: Int,
        activity: Activity,
        container: FrameLayout,
        layoutItem: WindowLayoutItemEntity
    ) {
        try {
            // 🏷️ 同步备注信息到SharedPreferences
            syncNoteToSharedPreferences(layoutItem, activity)

            // 📝 移除重复的文本格式保存（WindowLayoutModule已处理格式同步）

            // 使用布局参数创建TransformHandler
            val transformHandler = TransformHandler(activity, initialWidth = windowWidth, initialHeight = windowHeight).apply {
                // 直接设置布局参数，避免默认位置闪烁
                x = layoutItem.positionX
                y = layoutItem.positionY
            }

            // 设置文本连接（使用布局中保存的文本内容）
            val finalTextContent = layoutItem.textContent ?: textContent
            transformHandler.setupForTextConnection(textId, finalTextContent)

            // 🎯 关键修复：确保TransformHandler的基础尺寸与布局恢复尺寸一致
            transformHandler.updateBaseWindowSize(windowWidth, windowHeight)

            // 设置设备信息（使用最终的文本内容）
            transformHandler.setDeviceInfo(
                deviceName = finalTextContent,
                ipAddress = "本地设备",
                port = 0
            )

            // 设置回调
            setupTransformHandlerCallbacks(transformHandler)

            // 应用布局参数（除裁剪外）
            applyLayoutParametersExceptCrop(transformHandler, layoutItem)

            // 添加到容器
            val layoutParams = FrameLayout.LayoutParams(windowWidth, windowHeight)
            container.addView(transformHandler, layoutParams)
            dataModule.addWindowMapping(textId, transformHandler)

            AppLog.d("文本窗口创建完成（布局参数）: ID=$textId, 文本=$textContent, 尺寸: ${windowWidth}x${windowHeight}")

        } catch (e: Exception) {
            AppLog.e("创建文本窗口失败（布局参数）: ID=$textId", e)
        }
    }

    /**
     * 使用默认参数创建文本窗口
     */
    private fun createTextWindowWithDefaultParameters(
        textId: String,
        textContent: String,
        windowWidth: Int,
        windowHeight: Int,
        activity: Activity,
        container: FrameLayout
    ) {
        try {
            // 创建TransformHandler
            val transformHandler = TransformHandler(activity, initialWidth = windowWidth, initialHeight = windowHeight)

            // 设置文本连接
            transformHandler.setupForTextConnection(textId, textContent)

            // 🎯 关键修复：确保TransformHandler的基础尺寸与实际创建尺寸一致
            transformHandler.updateBaseWindowSize(windowWidth, windowHeight)

            // 设置设备信息
            transformHandler.setDeviceInfo(
                deviceName = textContent,
                ipAddress = "本地设备",
                port = 0
            )

            // 设置回调
            setupTransformHandlerCallbacks(transformHandler)

            // 添加到容器
            val layoutParams = FrameLayout.LayoutParams(windowWidth, windowHeight)
            container.addView(transformHandler, layoutParams)
            dataModule.addWindowMapping(textId, transformHandler)

            AppLog.d("文本窗口创建完成（默认参数）: ID=$textId, 文本=$textContent, 尺寸: ${windowWidth}x${windowHeight}")

        } catch (e: Exception) {
            AppLog.e("创建文本窗口失败（默认参数）: ID=$textId", e)
        }
    }

    // 📝 移除重复的文本格式恢复方法（WindowLayoutModule已统一处理格式同步）

    /**
     * 📁 应用完整的布局参数（复用WindowLayoutModule的精确逻辑）
     */
    private fun applyCompleteLayoutParameters(
        transformHandler: TransformHandler,
        layoutItem: WindowLayoutItemEntity
    ) {
        try {
            // 🎯 双重偏移修复：先处理裁剪状态，确保setPrecisionTransform在正确的裁剪状态下执行
            val hasCropParameters = layoutItem.cropRect != null
            if (hasCropParameters) {
                // 先应用裁剪参数，确保裁剪状态正确
                layoutItem.cropRect?.let { cropRectJson ->
                    try {
                        // 解析裁剪区域JSON（复用现有逻辑）
                        val left = cropRectJson.substringAfter("\"left\":").substringBefore(",").toFloat()
                        val top = cropRectJson.substringAfter("\"top\":").substringBefore(",").toFloat()
                        val right = cropRectJson.substringAfter("\"right\":").substringBefore(",").toFloat()
                        val bottom = cropRectJson.substringAfter("\"bottom\":").substringBefore("}").toFloat()
                        val cropRect = RectF(left, top, right, bottom)

                        transformHandler.setCropRectRatio(cropRect)
                        AppLog.d("【媒体创建】先应用裁剪参数，确保裁剪状态正确: ${layoutItem.deviceId}")
                    } catch (e: Exception) {
                        AppLog.w("【媒体创建】解析裁剪区域失败: $cropRectJson", e)
                        transformHandler.setCropRectRatio(null)
                    }
                }
            } else {
                // 布局没有裁剪参数，需要清除现有的裁剪状态
                transformHandler.setCropRectRatio(null)
                AppLog.d("【媒体创建】清除裁剪状态: ${layoutItem.deviceId}")
            }

            // 修复：使用精准变换方法，正确处理位置设置
            // 现在裁剪状态已经正确，setPrecisionTransform可以正确处理双重偏移问题
            transformHandler.setPrecisionTransform(
                x = layoutItem.positionX,
                y = layoutItem.positionY,
                scale = layoutItem.scaleFactor,
                rotation = layoutItem.rotationAngle
            )

            // 应用可见性
            AppLog.d("【媒体创建】窗口可见性设置: ${layoutItem.deviceId}, isVisible=${layoutItem.isVisible}")
            transformHandler.visibility = if (layoutItem.isVisible) android.view.View.VISIBLE else android.view.View.GONE

            // 应用视觉效果参数
            transformHandler.setCornerRadius(layoutItem.cornerRadius)

            // 恢复镜像和横屏状态（视觉效果）
            transformHandler.setMirrorEnabled(layoutItem.isMirrored)


            // 恢复边框相关状态
            transformHandler.setBorderEnabled(layoutItem.isBorderEnabled)
            transformHandler.setBorderColor(layoutItem.borderColor)
            transformHandler.setBorderWidth(layoutItem.borderWidth)

            // 设置透明度
            transformHandler.setWindowAlpha(layoutItem.alpha)

            // 🎬 如果是视频窗口，恢复播放状态
            if (layoutItem.deviceId.startsWith("video_")) {
                restoreVideoPlaybackStateForCreation(transformHandler, layoutItem)
            }

            AppLog.d("【媒体创建】完整布局参数应用完成: ${layoutItem.deviceId}, 最终位置(${transformHandler.getActualDisplayX()}, ${transformHandler.getActualDisplayY()})")

        } catch (e: Exception) {
            AppLog.e("【媒体创建】应用完整布局参数失败: ${layoutItem.deviceId}", e)
        }
    }

    /**
     * 🎬 恢复视频播放状态（用于媒体窗口创建）
     */
    private fun restoreVideoPlaybackStateForCreation(
        transformHandler: TransformHandler,
        layoutItem: WindowLayoutItemEntity
    ) {
        try {
            val connectionId = layoutItem.deviceId
            val activity = dataModule.getCurrentActivity() ?: return

            // 获取MediaSurfaceManager
            val mediaSurfaceManager = transformHandler.getMediaSurfaceManager()
            if (mediaSurfaceManager != null) {
                // 恢复播放状态到MediaSurfaceManager
                mediaSurfaceManager.setPlayEnabled(layoutItem.videoPlayEnabled)
                mediaSurfaceManager.setLoopCount(layoutItem.videoLoopCount)
                mediaSurfaceManager.setVolume(layoutItem.videoVolume)

                // 同时保存到SharedPreferences，确保UI显示一致
                val prefs = activity.getSharedPreferences("video_settings", android.content.Context.MODE_PRIVATE)
                prefs.edit().apply {
                    putBoolean("${connectionId}_play_enabled", layoutItem.videoPlayEnabled)
                    putInt("${connectionId}_loop_count", layoutItem.videoLoopCount)
                    putInt("${connectionId}_volume", layoutItem.videoVolume)
                    apply()
                }

                AppLog.d("【媒体创建】视频播放状态已恢复: $connectionId, 播放=${layoutItem.videoPlayEnabled}, 次数=${layoutItem.videoLoopCount}, 音量=${layoutItem.videoVolume}")
            } else {
                AppLog.w("【媒体创建】未找到MediaSurfaceManager，无法恢复视频播放状态: $connectionId")
            }
        } catch (e: Exception) {
            AppLog.e("【媒体创建】恢复视频播放状态失败: ${layoutItem.deviceId}", e)
        }
    }
}
