<EMAIL>,com.example.castapp.database.CastAppDatabaseNcom.example.castapp.manager.MediaProjectionManager.MediaProjectionCallbackImpl2com.example.castapp.model.RemoteReceiverConnection)com.example.castapp.network.SmartDataView(com.example.castapp.service.AudioService*com.example.castapp.service.CastingService4com.example.castapp.service.FloatingStopwatchService,com.example.castapp.service.ReceivingService1com.example.castapp.service.RemoteReceiverService#com.example.castapp.ui.MainActivity-com.example.castapp.ui.ReceiverDialogFragment+com.example.castapp.ui.SenderDialogFragment8com.example.castapp.ui.StopwatchWindow.TimeUpdateHandler0com.example.castapp.ui.adapter.ConnectionAdapterEcom.example.castapp.ui.adapter.ConnectionAdapter.ConnectionViewHolder8com.example.castapp.ui.adapter.CustomColorPaletteAdapterHcom.example.castapp.ui.adapter.CustomColorPaletteAdapter.ColorViewHolder2com.example.castapp.ui.adapter.LayerManagerAdapterBcom.example.castapp.ui.adapter.LayerManagerAdapter.LayerViewHolderJcom.example.castapp.ui.adapter.LayerManagerAdapter.ItemTouchHelperCallbackEcom.example.castapp.ui.adapter.LayerManagerAdapter.WindowDiffCallback2com.example.castapp.ui.adapter.LayoutDetailAdapterCcom.example.castapp.ui.adapter.LayoutDetailAdapter.DetailViewHolderEcom.example.castapp.ui.adapter.LayoutDetailAdapter.DetailDiffCallback1com.example.castapp.ui.adapter.LayoutDiffCallback0com.example.castapp.ui.adapter.LayoutListAdapterAcom.example.castapp.ui.adapter.LayoutListAdapter.LayoutViewHolder:com.example.castapp.ui.adapter.RemoteReceiverDeviceAdapterEcom.example.castapp.ui.adapter.RemoteReceiverDeviceAdapter.ViewHolder2com.example.castapp.ui.adapter.RemoteSenderAdapter=com.example.castapp.ui.adapter.RemoteSenderAdapter.ViewHolder8com.example.castapp.ui.adapter.RemoteSenderDeviceAdapterCcom.example.castapp.ui.adapter.RemoteSenderDeviceAdapter.ViewHolder4com.example.castapp.ui.adapter.RemoteTabPagerAdapter3com.example.castapp.ui.adapter.WindowManagerAdapterDcom.example.castapp.ui.adapter.WindowManagerAdapter.WindowViewHolderFcom.example.castapp.ui.adapter.WindowManagerAdapter.WindowDiffCallback4com.example.castapp.ui.dialog.AddMediaDialogFragment;com.example.castapp.ui.dialog.AddRemoteReceiverDeviceDialog9com.example.castapp.ui.dialog.AddRemoteSenderDeviceDialog,com.example.castapp.ui.dialog.DirectorDialog<com.example.castapp.ui.dialog.EditRemoteReceiverDeviceDialog:com.example.castapp.ui.dialog.EditRemoteSenderDeviceDialog>com.example.castapp.ui.dialog.FontFilePickerDialog.FileAdapterMcom.example.castapp.ui.dialog.FontFilePickerDialog.FileAdapter.FileViewHolder<com.example.castapp.ui.dialog.FontSettingsDialog.FontAdapterKcom.example.castapp.ui.dialog.FontSettingsDialog.FontAdapter.FontViewHolderDcom.example.castapp.ui.dialog.FontSizeSettingsDialog.FontSizeAdapterWcom.example.castapp.ui.dialog.FontSizeSettingsDialog.FontSizeAdapter.FontSizeViewHolder0com.example.castapp.ui.dialog.LayerManagerDialogNcom.example.castapp.ui.dialog.LetterSpacingSettingsDialog.LetterSpacingAdapterYcom.example.castapp.ui.dialog.LetterSpacingSettingsDialog.LetterSpacingAdapter.ViewHolderJcom.example.castapp.ui.dialog.LineSpacingSettingsDialog.LineSpacingAdapterUcom.example.castapp.ui.dialog.LineSpacingSettingsDialog.LineSpacingAdapter.ViewHolder8com.example.castapp.ui.dialog.RemoteControlManagerDialog6com.example.castapp.ui.dialog.RemoteLayerManagerDialogBcom.example.castapp.ui.dialog.RemoteMediaSelectionDialog.MediaType9com.example.castapp.ui.dialog.RemoteReceiverControlDialogAcom.example.castapp.ui.dialog.RemoteReceiverSettingsControlDialog7com.example.castapp.ui.dialog.RemoteSenderControlDialog7com.example.castapp.ui.dialog.RemoteWindowManagerDialog/com.example.castapp.ui.dialog.SaveOptionsDialog1com.example.castapp.ui.dialog.WindowManagerDialog9com.example.castapp.ui.fragment.RemoteReceiverTabFragment7com.example.castapp.ui.fragment.RemoteSenderTabFragment;com.example.castapp.ui.helper.LayoutItemTouchHelperCallback+com.example.castapp.ui.view.CropOverlayView6com.example.castapp.ui.view.CropOverlayView.HandleType4com.example.castapp.ui.view.CropVisualizationOverlay=com.example.castapp.ui.view.CropVisualizationOverlay.DragMode.com.example.castapp.ui.view.GestureOverlayView1com.example.castapp.ui.view.PrecisionControlPanel.com.example.castapp.ui.view.TextEditBorderView7com.example.castapp.ui.view.TextEditBorderView.DragMode)com.example.castapp.ui.view.TextEditPanel9com.example.castapp.ui.view.TextEditPanel.FontSizeAdapter>com.example.castapp.ui.view.TextEditPanel.LetterSpacingAdapter<com.example.castapp.ui.view.TextEditPanel.LineSpacingAdapter;com.example.castapp.ui.view.TextEditPanel.FontFamilyAdapter>com.example.castapp.ui.view.TextEditPanel.TextAlignmentAdapter*com.example.castapp.ui.view.TextWindowView.com.example.castapp.ui.view.CustomTypefaceSpan<com.example.castapp.ui.view.WindowContainerVisualizationViewQcom.example.castapp.ui.view.WindowContainerVisualizationView.ScaleGestureListenerTcom.example.castapp.ui.view.WindowContainerVisualizationView.RotationGestureListener<com.example.castapp.ui.view.WindowVisualizationContainerView6com.example.castapp.ui.windowsettings.TransformHandlerAcom.example.castapp.ui.windowsettings.TransformHandler.ResizeModeKcom.example.castapp.ui.windowsettings.TransformHandler.ScaleGestureListenerNcom.example.castapp.ui.windowsettings.TransformHandler.RotationGestureListener+com.example.castapp.utils.LetterSpacingSpan)com.example.castapp.utils.LineSpacingSpan>com.example.castapp.utils.NotificationManager.NotificationType$com.example.castapp.utils.StrokeSpan+com.example.castapp.viewmodel.MainViewModel/com.example.castapp.viewmodel.ReceiverViewModel-com.example.castapp.viewmodel.SenderViewModelOcom.example.castapp.viewmodel.SenderViewModel.ResolutionAdjustmentState.SuccessNcom.example.castapp.viewmodel.SenderViewModel.ResolutionAdjustmentState.FailedRcom.example.castapp.viewmodel.SenderViewModel.ResolutionAdjustmentState.InProgressQcom.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate.BitrateUpdateTcom.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate.ResolutionUpdatePcom.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate.VolumeUpdateTcom.example.castapp.viewmodel.SenderViewModel.RemoteControlUIUpdate.ConnectionToggle-com.example.castapp.websocket.WebSocketClient-com.example.castapp.websocket.WebSocketServer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           